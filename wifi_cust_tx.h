#ifndef WIFI_CUST_TX
#define WIFI_CUST_TX

#include <Arduino.h>

// 定义解除认证帧的结构体
typedef struct {
  uint16_t frame_control = 0xC0;      // 帧控制字段，设置为解除认证类型
  uint16_t duration = 0xFFFF;         // 持续时间字段
  uint8_t destination[6];             // 目标MAC地址
  uint8_t source[6];                  // 源MAC地址
  uint8_t access_point[6];            // 接入点MAC地址
  const uint16_t sequence_number = 0;  // 序列号
  uint16_t reason = 0x06;             // 解除认证原因码
} DeauthFrame;

// 定义信标帧的结构体
typedef struct {
  uint16_t frame_control = 0x80;      // 帧控制字段，设置为信标类型
  uint16_t duration = 0;              // 持续时间字段
  uint8_t destination[6];             // 目标MAC地址
  uint8_t source[6];                  // 源MAC地址
  uint8_t access_point[6];            // 接入点MAC地址
  const uint16_t sequence_number = 0;  // 序列号
  const uint64_t timestamp = 0;       // 时间戳
  uint16_t beacon_interval = 0x64;    // 信标间隔
  uint16_t ap_capabilities = 0x21;    // 接入点能力信息
  const uint8_t ssid_tag = 0;         // SSID标签
  uint8_t ssid_length = 0;            // SSID长度
  uint8_t ssid[255];                  // SSID内容
} BeaconFrame;

// 定义Probe响应帧的结构体
typedef struct {
  uint16_t frame_control = 0x50;      // 帧控制字段，设置为Probe响应类型
  uint16_t duration = 0;              // 持续时间字段
  uint8_t destination[6];             // 目标MAC地址
  uint8_t source[6];                  // 源MAC地址
  uint8_t access_point[6];            // 接入点MAC地址
  const uint16_t sequence_number = 0;  // 序列号
  const uint64_t timestamp = 0;       // 时间戳
  uint16_t beacon_interval = 0x64;    // 信标间隔
  uint16_t ap_capabilities = 0x21;    // 接入点能力信息
  const uint8_t ssid_tag = 0;         // SSID标签
  uint8_t ssid_length = 0;            // SSID长度
  uint8_t ssid[255];                  // SSID内容
} ProbeResponseFrame;

// 定义认证帧的结构体
typedef struct {
  uint16_t frame_control = 0xB0;      // 帧控制字段，设置为认证类型
  uint16_t duration = 0x013A;         // 持续时间字段
  uint8_t destination[6];             // 目标MAC地址（AP的MAC）
  uint8_t source[6];                  // 源MAC地址（伪造的客户端MAC）
  uint8_t access_point[6];            // 接入点MAC地址
  const uint16_t sequence_number = 0;  // 序列号
  uint16_t auth_algorithm = 0;        // 认证算法（0=开放系统）
  uint16_t auth_seq = 1;              // 认证序列号（1=请求）
  uint16_t status_code = 0;           // 状态码（0=成功）
} AuthFrame;

// 定义关联请求帧的结构体
typedef struct {
  uint16_t frame_control = 0x00;      // 帧控制字段，设置为关联请求类型
  uint16_t duration = 0x013A;         // 持续时间字段
  uint8_t destination[6];             // 目标MAC地址（AP的MAC）
  uint8_t source[6];                  // 源MAC地址（伪造的客户端MAC）
  uint8_t access_point[6];            // 接入点MAC地址
  const uint16_t sequence_number = 0;  // 序列号
  uint16_t capabilities = 0x0431;     // 能力信息
  uint16_t listen_interval = 0x000A;  // 监听间隔
  const uint8_t ssid_tag = 0;         // SSID标签
  uint8_t ssid_length = 0;            // SSID长度
  uint8_t ssid[255];                  // SSID内容
} AssocFrame;

// 定义EAPOL-Start帧的结构体
typedef struct {
  uint16_t frame_control = 0x08B0;    // 帧控制字段，设置为数据类型
  uint16_t duration = 0x013A;         // 持续时间字段
  uint8_t destination[6];             // 目标MAC地址（AP的MAC）
  uint8_t source[6];                  // 源MAC地址（伪造的客户端MAC）
  uint8_t access_point[6];            // 接入点MAC地址
  const uint16_t sequence_number = 0;  // 序列号
  // LLC头
  uint8_t llc_dsap = 0xAA;            // LLC DSAP
  uint8_t llc_ssap = 0xAA;            // LLC SSAP
  uint8_t llc_control = 0x03;         // LLC控制
  uint8_t llc_oui[3] = {0x00, 0x00, 0x00}; // LLC OUI
  uint16_t llc_type = 0x888E;         // LLC类型 (EAPOL)
  // EAPOL头
  uint8_t eapol_version = 0x01;       // EAPOL版本
  uint8_t eapol_type = 0x01;          // EAPOL类型 (Start)
  uint16_t eapol_length = 0x0000;     // EAPOL长度 (0表示Start帧)
} EapolStartFrame;

// 定义Commit请求帧的结构体
typedef struct {
  uint16_t frame_control = 0x00D0;    // 帧控制字段，设置为Commit请求类型
  uint16_t duration = 0x013A;         // 持续时间字段
  uint8_t destination[6];             // 目标MAC地址（AP的MAC）
  uint8_t source[6];                  // 源MAC地址（伪造的客户端MAC）
  uint8_t access_point[6];            // 接入点MAC地址
  const uint16_t sequence_number = 0;  // 序列号
  // Commit请求帧特有字段
  uint8_t category = 0x0F;            // 类别代码 (Fast BSS Transition)
  uint8_t action = 0x01;              // 动作代码 (Commit请求)
  // 椭圆曲线密码学参数
  uint8_t scalar[32];                 // 合法的Scalar值
  uint8_t group_id[2];                // 合法的Group ID
  uint8_t finite_field[32];           // 合法的Finite Field值
  uint8_t reserved[16];               // 保留字段，用于其他参数
} CommitFrame;

// 从闭源库导入所需的C函数
// 注意：函数定义可能不是100%准确，因为在编译过程中类型信息会丢失
extern uint8_t* rltk_wlan_info;
extern "C" void* alloc_mgtxmitframe(void* ptr);
extern "C" void update_mgntframe_attrib(void* ptr, void* frame_control);
extern "C" int dump_mgntframe(void* ptr, void* frame_control);

// 函数声明
void wifi_tx_raw_frame(void* frame, size_t length);
void wifi_tx_deauth_frame(void* src_mac, void* dst_mac, uint16_t reason = 0x06);
void wifi_tx_beacon_frame(void* src_mac, void* dst_mac, const char *ssid);
void wifi_tx_noise_frame(void* src_mac, void* dst_mac, uint8_t data_length);
void wifi_tx_auth_flood(void* ap_mac, int num_packets, int delay_ms);
void wifi_tx_assoc_flood(void* ap_mac, const char *ssid, int num_packets, int delay_ms);
void wifi_tx_eapol_flood(void* ap_mac, int num_packets, int delay_ms);
void wifi_tx_probe_response(void* ap_mac, void* src_mac, const char *ssid);
void wifi_tx_hybrid_attack(uint8_t* target_bssid, const char* ssid, int num_packets);
void wifi_tx_router_exhaustion(uint8_t* bssid, const char* ssid, int num_clients);
void wifi_tx_assoc_request(uint8_t* bssid, uint8_t* src_mac, const char* ssid);
void wifi_tx_auth_frame(uint8_t* bssid, uint8_t* src_mac);
void wifi_tx_advanced_hybrid_attack(uint8_t* target_bssid, const char* ssid, int* packets_sent);
void wifi_tx_commit_attack(uint8_t* bssid, int num_packets, int delay_ms);

// 增强版信标和认证帧函数
void wifi_tx_beacon_frame_Privacy_RSN_IE(void* src_mac, void* dst_mac, const char *ssid);
void wifi_tx_auth_frame_enhanced(void* src_mac, void* dst_mac, uint16_t seq);

// 生成随机MAC地址的辅助函数
void generate_random_mac(uint8_t* mac);

#endif
