#ifndef PROTECT_H
#define PROTECT_H

#include <WiFi.h>
#include <FlashMemory.h>
#include "mbedtls/sha256.h"
#include "WiFiServer.h"
#include "WiFiClient.h"
#include "DNSServer.h"
#include "sys_api.h"

#ifndef min
#define min(a, b) ((a) < (b) ? (a) : (b))
#endif

#define HASH_ADDRESS 0x00100000  // Flash基地址
#define HASH_OFFSET  0x0000      // Flash内的偏移量
#define HASH_LENGTH  64          // SHA256哈希值的十六进制字符串长度
#define MAC_OFFSET   0x0040      // MAC地址在Flash中的偏移量
#define MAC_LENGTH   12          // MAC地址的十六进制字符串长度

const String SALT = "SALTYFISH";

String globalMACAddress = "";

// 激活保护机制状态变量
bool deviceActivated = false;           // 设备激活状态
bool activationAPRunning = false;       // 激活AP是否运行中
WiFiServer* activationWebServer = nullptr;  // 激活Web服务器指针
DNSServer* activationDnsServer = nullptr;   // 激活DNS服务器指针
unsigned long lastActivationCheck = 0;  // 上次激活检查时间
const unsigned long ACTIVATION_CHECK_INTERVAL = 100; // 激活检查间隔（100ms）

// 严格的并发控制机制
volatile bool isProcessingActivation = false; // 防止并发激活请求
volatile unsigned long activationLockTime = 0; // 激活锁定时间
volatile unsigned long lastActivationTime = 0; // 上次激活请求时间
volatile unsigned long activationRequestCounter = 0; // 激活请求计数器
const unsigned long MIN_ACTIVATION_INTERVAL = 1000; // 最小激活间隔（1秒）
const unsigned long ACTIVATION_LOCK_TIMEOUT = 10000; // 激活锁超时（10秒）

// 尝试次数限制
int activationAttempts = 0;
const int MAX_ATTEMPTS = 100;
const unsigned long RESET_INTERVAL = 5 * 60 * 1000; // 5分钟（毫秒）
unsigned long lastResetTime = 0;

// 前向声明 - 需要在主文件中定义的显示函数
extern void setColorScheme();
extern void resetTextColor();
extern void drawHighlightBox(int x, int y, int width, int height);
extern bool isButtonUp();
extern bool isButtonDown();
extern unsigned long OK_BACK_DELAY;
extern unsigned long UP_DOWN_DELAY;

// 功能限制检查函数
bool isFeatureAllowed(int featureType) {
    if (deviceActivated) {
        return true; // 已激活，允许所有功能
    }

    // 未激活时，只允许以下功能：
    // 0: Deauth攻击, 1: Beacon攻击, 2: 扫描功能, 3: 选择功能
    switch (featureType) {
        case 0: // Deauth攻击
        case 1: // Beacon攻击
        case 2: // 扫描功能
        case 3: // 选择功能
            return true;
        default:
            return false; // 其他功能被限制
    }
}

// 显示未激活弹窗 - 声明，实现在主文件中
void showActivationDialog();

// 原子性操作函数
bool atomicTryLockActivation() {
  unsigned long currentTime = millis();

  // 检查锁是否超时
  if (isProcessingActivation && (currentTime - activationLockTime > ACTIVATION_LOCK_TIMEOUT)) {
    Serial.println("激活锁超时，强制释放");
    isProcessingActivation = false;
  }

  // 尝试获取锁
  if (!isProcessingActivation) {
    isProcessingActivation = true;
    activationLockTime = currentTime;
    activationRequestCounter++;
    Serial.print("获取激活锁，请求ID: ");
    Serial.println(activationRequestCounter);
    return true;
  }

  return false;
}

void atomicUnlockActivation() {
  isProcessingActivation = false;
  Serial.println("释放激活锁");
}

// 检查并重置尝试次数
void checkAndResetAttempts() {
  unsigned long currentTime = millis();

  // 处理时间溢出（约49天后millis()会溢出）
  if (currentTime < lastResetTime) {
    lastResetTime = currentTime;
  }

  // 检查是否需要重置
  if (currentTime - lastResetTime >= RESET_INTERVAL) {
    activationAttempts = 0;
    lastResetTime = currentTime;
    Serial.println("尝试次数已重置");
  }
}

// 检查是否超过最大尝试次数
bool isAttemptsExceeded() {
  checkAndResetAttempts();
  return activationAttempts >= MAX_ATTEMPTS;
}

// URL解码函数
String urlDecode(String str) {
  String decoded = "";
  unsigned int len = str.length();
  unsigned int i = 0;
  while (i < len) {
    char decodedChar;
    if (str[i] == '%' && i + 2 < len) {
      // 解析十六进制字符
      char hex[3];
      hex[0] = str[i + 1];
      hex[1] = str[i + 2];
      hex[2] = '\0';
      decodedChar = (char)strtol(hex, NULL, 16);
      i += 3;
    } else if (str[i] == '+') {
      decodedChar = ' ';
      i++;
    } else {
      decodedChar = str[i];
      i++;
    }
    decoded += decodedChar;
  }
  return decoded;
}

// 激活页面HTML模板
const char* activationHTML = R"rawliteral(
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BW16设备激活</title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .mac-display {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .copy-btn {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        .copy-btn:active {
            transform: translateY(0);
        }
        .copy-success {
            background: #28a745 !important;
        }
        .qq-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 12px;
            margin: 15px 0;
            text-align: center;
            font-size: 14px;
            color: #1565c0;
            line-height: 1.4;
        }
        .qq-number {
            font-weight: bold;
            color: #0d47a1;
            font-family: monospace;
            font-size: 15px;
        }
        .input-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: white;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 10px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            display: none;
            margin-top: 10px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 - 手机端 (小于768px) */
        @media screen and (max-width: 767px) {
            body {
                padding: 10px;
                min-height: 100vh;
            }
            .container {
                padding: 20px;
                max-width: 100%;
                margin: 0;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }
            h1 {
                font-size: 20px;
                margin-bottom: 20px;
            }
            .mac-display {
                flex-direction: column;
                gap: 10px;
                padding: 12px;
                font-size: 14px;
            }
            .copy-btn {
                width: 100%;
                margin-left: 0;
                padding: 10px;
                font-size: 16px;
            }
            input[type="text"] {
                font-size: 16px;
                padding: 14px;
            }
            .btn {
                width: 100%;
                padding: 14px;
                font-size: 18px;
                margin-top: 15px;
            }
            .status {
                font-size: 14px;
                padding: 12px;
            }
            .qq-info {
                font-size: 13px;
                padding: 10px;
                margin: 12px 0;
            }
            .qq-number {
                font-size: 14px;
            }
        }

        /* 响应式设计 - 平板端 (768px - 1024px) */
        @media screen and (min-width: 768px) and (max-width: 1024px) {
            body {
                padding: 30px;
            }
            .container {
                max-width: 500px;
                padding: 35px;
            }
            h1 {
                font-size: 26px;
            }
            .mac-display {
                font-size: 16px;
                padding: 18px;
            }
            .copy-btn {
                padding: 10px 15px;
                font-size: 15px;
            }
            input[type="text"] {
                font-size: 17px;
                padding: 14px;
            }
            .btn {
                padding: 14px 35px;
                font-size: 17px;
            }
            .qq-info {
                font-size: 15px;
                padding: 14px;
            }
            .qq-number {
                font-size: 16px;
            }
        }

        /* 响应式设计 - 电脑端 (大于1024px) */
        @media screen and (min-width: 1025px) {
            body {
                padding: 40px;
            }
            .container {
                max-width: 450px;
                padding: 40px;
            }
            h1 {
                font-size: 28px;
                margin-bottom: 35px;
            }
            .mac-display {
                font-size: 18px;
                padding: 20px;
            }
            .copy-btn {
                padding: 10px 16px;
                font-size: 14px;
            }
            input[type="text"] {
                font-size: 16px;
                padding: 15px;
            }
            .btn {
                padding: 15px 40px;
                font-size: 16px;
            }
            .btn:hover {
                transform: translateY(-3px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }
            .qq-info {
                font-size: 15px;
                padding: 15px;
                margin: 20px 0;
            }
            .qq-number {
                font-size: 16px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .copy-btn:hover,
            .btn:hover {
                transform: none;
            }
            .copy-btn:active,
            .btn:active {
                transform: scale(0.98);
                transition: transform 0.1s;
            }
            /* 增大触摸目标 */
            .copy-btn,
            .btn {
                min-height: 44px;
            }
            input[type="text"] {
                min-height: 44px;
            }
        }

        /* 横屏手机优化 */
        @media screen and (max-width: 767px) and (orientation: landscape) {
            body {
                padding: 5px;
                align-items: flex-start;
                padding-top: 20px;
            }
            .container {
                margin: 0 auto;
                max-height: 90vh;
                overflow-y: auto;
            }
            h1 {
                font-size: 18px;
                margin-bottom: 15px;
            }
        }

        /* 高分辨率屏幕优化 */
        @media screen and (min-resolution: 2dppx) {
            .container {
                border: 0.5px solid rgba(0,0,0,0.1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 BW16设备激活</h1>

        <div class="mac-display">
            <span id="macAddress">MAC: %MAC_ADDRESS%</span>
            <button class="copy-btn" onclick="copyMacAddress()" id="macCopyBtn">复制</button>
        </div>

        <div class="input-group">
            <input type="text" id="activationCode" placeholder="请输入激活码" maxlength="64">
        </div>

        <div class="qq-info">
            激活码找QQ群:<span class="qq-number">887958737</span>的浪木或者Ghost购买。
        </div>

        <button class="btn" onclick="activateDevice()" id="activateBtn">激活设备</button>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在激活设备...</p>
        </div>

        <div id="status"></div>
    </div>

    <script>
        let isActivating = false; // 防止重复提交的标志
        let lastActivationTime = 0; // 上次激活时间
        let activationRequestId = 0; // 激活请求ID
        let pendingRequestId = null; // 当前待处理的请求ID
        let activationLockTime = 0; // 激活锁定时间
        const ACTIVATION_LOCK_TIMEOUT = 30000; // 前端锁超时（30秒）
        let pendingRequest = null; // 当前进行中的请求
        let activationLocked = false; // 激活锁定状态

        // 前端原子性锁机制
        function tryLockActivation() {
            const currentTime = Date.now();

            // 检查锁是否超时
            if (isActivating && (currentTime - activationLockTime > ACTIVATION_LOCK_TIMEOUT)) {
                console.log('前端激活锁超时，强制释放');
                isActivating = false;
                pendingRequestId = null;
            }

            // 尝试获取锁
            if (!isActivating) {
                isActivating = true;
                activationLockTime = currentTime;
                activationRequestId++;
                pendingRequestId = activationRequestId;
                console.log('获取前端激活锁，请求ID:', activationRequestId);
                return true;
            }

            console.log('前端激活锁已被占用，请求ID:', pendingRequestId);
            return false;
        }

        function unlockActivation() {
            isActivating = false;
            pendingRequestId = null;
            console.log('释放前端激活锁');
        }

        // 清除之前的状态显示
        function clearPreviousStatus() {
            const statusDiv = document.getElementById('status');
            const loadingDiv = document.getElementById('loading');

            // 清除状态显示
            if (statusDiv) {
                statusDiv.innerHTML = '';
                statusDiv.className = ''; // 清除所有CSS类
            }

            // 隐藏加载动画
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
        }

        function activateDevice() {
            const code = document.getElementById('activationCode').value.trim();
            const statusDiv = document.getElementById('status');
            const loadingDiv = document.getElementById('loading');
            const activateBtn = document.getElementById('activateBtn');

            // 第一层防护：检查激活锁定状态
            if (activationLocked) {
                console.log('激活功能已锁定，忽略请求');
                return;
            }

            // 第二层防护：检查激活进行状态
            if (isActivating) {
                console.log('激活正在进行中，忽略重复请求');
                return;
            }

            // 第三层防护：检查是否有进行中的请求
            if (pendingRequest) {
                console.log('存在进行中的请求，忽略新请求');
                return;
            }

            // 第四层防护：检查时间间隔
            const currentTime = Date.now();
            if (currentTime - lastActivationTime < 1000) {
                console.log('激活请求过于频繁，忽略请求');
                return;
            }

            if (code.length !== 64) {
                showStatus('请输入正确的激活码', 'error');
                return;
            }

            // 清除之前的状态显示
            clearPreviousStatus();

            // 设置激活状态和时间
            isActivating = true;
            activationLocked = true; // 锁定激活功能
            lastActivationTime = currentTime;
            activationRequestId++;
            const currentRequestId = activationRequestId;

            // 显示加载状态
            activateBtn.disabled = true;
            activateBtn.textContent = '激活中...';
            activateBtn.style.pointerEvents = 'none'; // 完全禁用点击
            loadingDiv.style.display = 'block';
            statusDiv.innerHTML = '';

            console.log('开始激活请求，ID:', currentRequestId, '激活已锁定');

            // 创建请求控制器
            const abortController = new AbortController();

            // 发送激活请求
            pendingRequest = fetch('/activate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'code=' + encodeURIComponent(code) + '&requestId=' + currentRequestId,
                signal: abortController.signal
            })
            .then(response => {
                // 检查请求是否仍然有效
                if (currentRequestId !== activationRequestId) {
                    console.log('请求已过期，忽略响应，当前ID:', currentRequestId, '最新ID:', activationRequestId);
                    pendingRequest = null;
                    return;
                }

                if (!response.ok) {
                    throw new Error('网络响应错误: ' + response.status);
                }
                return response.text();
            })
            .then(data => {
                // 清理请求状态
                pendingRequest = null;

                // 再次检查请求是否仍然有效
                if (currentRequestId !== activationRequestId) {
                    console.log('响应处理时请求已过期，忽略，当前ID:', currentRequestId, '最新ID:', activationRequestId);
                    return;
                }

                // 最终状态检查：确保这是唯一的有效响应
                if (!isActivating || !activationLocked) {
                    console.log('状态异常，忽略响应，isActivating:', isActivating, 'activationLocked:', activationLocked);
                    return;
                }

                // 清理响应数据，去除可能的空白字符
                const cleanData = data.trim();
                console.log('服务器响应 (ID:' + currentRequestId + '):', cleanData);

                if (cleanData === 'SUCCESS' || cleanData.includes('SUCCESS')) {
                    showStatus('激活成功！设备将在3秒后自动重启...', 'success');
                    // 激活成功后不恢复按钮状态，防止重复操作
                    setTimeout(() => {
                        showStatus('设备正在重启，请稍候...', 'info');
                    }, 3000);
                } else if (cleanData === 'FAILED' || cleanData.includes('FAILED')) {
                    showStatus('激活失败：激活码无效，请检查后重试', 'error');
                    resetActivationState(activateBtn, loadingDiv);
                } else if (cleanData === 'BUSY' || cleanData.includes('BUSY')) {
                    showStatus('激活请求正在处理中，请稍候...', 'error');
                    resetActivationState(activateBtn, loadingDiv);
                } else if (cleanData === 'TOO_FREQUENT' || cleanData.includes('TOO_FREQUENT')) {
                    showStatus('请求过于频繁，请稍后再试', 'error');
                    resetActivationState(activateBtn, loadingDiv);
                } else if (cleanData.startsWith('LIMIT_EXCEEDED:')) {
                    const secondsLeft = cleanData.split(':')[1];
                    showStatus('激活尝试次数已达上限(100次)，正在准备倒计时...', 'error');
                    disableActivationPermanently(activateBtn, loadingDiv, secondsLeft);
                } else {
                    showStatus('激活失败：服务器响应异常 (' + cleanData + ')', 'error');
                    resetActivationState(activateBtn, loadingDiv);
                }
            })
            .catch(error => {
                // 清理请求状态
                pendingRequest = null;

                // 检查请求是否仍然有效
                if (currentRequestId !== activationRequestId) {
                    console.log('错误处理时请求已过期，忽略，当前ID:', currentRequestId, '最新ID:', activationRequestId);
                    return;
                }

                console.error('激活请求错误 (ID:' + currentRequestId + '):', error);
                showStatus('网络错误，请重试', 'error');
                resetActivationState(activateBtn, loadingDiv);
            });
        }

        // 重置激活状态（仅在失败时调用）
        function resetActivationState(activateBtn, loadingDiv) {
            isActivating = false;
            activationLocked = false; // 解锁激活功能
            pendingRequest = null; // 清理请求状态
            loadingDiv.style.display = 'none';
            activateBtn.disabled = false;
            activateBtn.textContent = '激活设备';
            activateBtn.style.pointerEvents = 'auto'; // 恢复点击
            activateBtn.style.background = ''; // 恢复原始样式
            console.log('激活状态已重置，功能已解锁');
        }

        // 永久禁用激活（达到尝试次数上限）
        function disableActivationPermanently(activateBtn, loadingDiv, secondsLeft) {
            isActivating = true; // 保持激活状态，防止重试
            loadingDiv.style.display = 'none';
            activateBtn.disabled = true;
            activateBtn.textContent = '已达尝试上限';
            activateBtn.style.background = '#dc3545';

            // 开始倒计时显示
            startCountdown(parseInt(secondsLeft));
        }

        // 倒计时功能
        function startCountdown(totalSeconds) {
            const statusDiv = document.getElementById('status');
            let remainingSeconds = totalSeconds;

            function updateCountdown() {
                const minutes = Math.floor(remainingSeconds / 60);
                const seconds = remainingSeconds % 60;
                const timeString = minutes + '分' + (seconds < 10 ? '0' : '') + seconds + '秒';

                if (statusDiv) {
                    statusDiv.innerHTML = '已达最大尝试次数，请等待 ' + timeString + ' 后重试';
                    statusDiv.className = 'status error';
                }

                if (remainingSeconds <= 0) {
                    // 倒计时结束，刷新页面
                    location.reload();
                    return;
                }

                remainingSeconds--;
                setTimeout(updateCountdown, 1000);
            }

            updateCountdown();
        }



        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            if (statusDiv) {
                statusDiv.innerHTML = message;
                // 清除之前的类，然后设置新的类
                statusDiv.className = 'status ' + type;
            }
        }

        // 复制MAC地址
        function copyMacAddress() {
            const macElement = document.getElementById('macAddress');
            const macText = macElement.textContent.replace('MAC: ', ''); // 只复制MAC地址部分
            const copyBtn = document.getElementById('macCopyBtn');

            if (navigator.clipboard && window.isSecureContext) {
                // 现代浏览器的异步API
                navigator.clipboard.writeText(macText).then(function() {
                    showCopySuccess(copyBtn, '已复制');
                }).catch(function() {
                    fallbackCopyTextToClipboard(macText, copyBtn);
                });
            } else {
                // 兼容旧浏览器
                fallbackCopyTextToClipboard(macText, copyBtn);
            }
        }

        // 兼容性复制函数
        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess(button, '已复制');
            } catch (err) {
                showCopySuccess(button, '复制失败');
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功状态
        function showCopySuccess(button, message) {
            const originalText = button.textContent;
            button.textContent = message;
            button.classList.add('copy-success');

            setTimeout(function() {
                button.textContent = originalText;
                button.classList.remove('copy-success');
            }, 2000);
        }

        // 回车键激活
        document.getElementById('activationCode').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                activateDevice();
            }
        });

        // 输入框获得焦点时清除之前的错误状态
        document.getElementById('activationCode').addEventListener('focus', function(e) {
            const statusDiv = document.getElementById('status');
            if (statusDiv && statusDiv.classList.contains('error')) {
                clearPreviousStatus();
            }
        });

        // 输入框内容改变时清除错误状态
        document.getElementById('activationCode').addEventListener('input', function(e) {
            const statusDiv = document.getElementById('status');
            if (statusDiv && statusDiv.classList.contains('error')) {
                clearPreviousStatus();
            }
        });
    </script>
</body>
</html>
)rawliteral";

String readStringFromFlash(uint32_t address, size_t length);
void writeStringToFlash(uint32_t address, const String& str, size_t length);

// 从Flash读取MAC
String readMACFromFlash() {
  return readStringFromFlash(MAC_OFFSET, MAC_LENGTH);
}

// 将MAC写入Flash
void writeMACToFlash(String mac) {
  Serial.print("正在写入MAC地址到Flash: ");
  Serial.println(mac);
  writeStringToFlash(MAC_OFFSET, mac, MAC_LENGTH);
  Serial.println("MAC地址已成功写入Flash存储器");
}

// 将二进制SHA256转换为十六进制字符串
String toHexString(const unsigned char *hash, size_t length = 32) {
  char hex[65];
  for (size_t i = 0; i < length; i++) {
    sprintf(&hex[i * 2], "%02x", hash[i]);
  }
  hex[64] = '\0';
  return String(hex);
}

// 使用mbedTLS执行2次SHA256哈希
String multipleSHA256(String data) {
  unsigned char hash[32];
  String currentData = data;

  // 执行2次SHA256哈希
  for (int i = 0; i < 2; i++) {
    mbedtls_sha256((const unsigned char*)currentData.c_str(), currentData.length(), hash, 0);
    currentData = toHexString(hash);
  }

  return currentData;
}

// 获取持久化的MAC地址作为不带分隔符的大写字符串
String getMACAddress() {
  // 如果已经加载过MAC地址，直接返回
  if (globalMACAddress.length() == 12) {
    return globalMACAddress;
  }

  // 首先尝试从Flash读取已保存的MAC地址
  String storedMAC = readMACFromFlash();

  bool isValidMAC = false;
  if (storedMAC.length() == 12) {
    // 检查字符格式
    bool formatValid = true;
    for (int i = 0; i < 12; i++) {
      char c = storedMAC.charAt(i);
      if (!((c >= '0' && c <= '9') || (c >= 'A' && c <= 'F'))) {
        formatValid = false;
        break;
      }
    }

    if (formatValid) {
      isValidMAC = true;
    }
  }

  if (isValidMAC) {
    globalMACAddress = storedMAC;
    return storedMAC;
  }

  Serial.println("Flash中无有效MAC地址，正在生成新的随机MAC地址...");

  // 随机种子生成算法
  unsigned long finalSeed = 0;

  for (int i = 0; i < 20; i++) {
    unsigned long entropy = micros();
    finalSeed ^= entropy;
    finalSeed = (finalSeed << 1) | (finalSeed >> 31); // 循环左移

    delayMicroseconds(50 + (i * 7) % 100);
  }

  finalSeed ^= millis();

  finalSeed ^= (unsigned long)&finalSeed; // 栈地址

  finalSeed ^= (finalSeed << 13);
  finalSeed ^= (finalSeed >> 17);
  finalSeed ^= (finalSeed << 5);
  finalSeed ^= (finalSeed >> 11);
  finalSeed ^= (finalSeed << 7);
  finalSeed ^= (finalSeed >> 19);

  if (finalSeed == 0 || finalSeed == 0xFFFFFFFF) {
    finalSeed = 0x9E3779B9;
  }

  randomSeed(finalSeed);

  // 生成随机MAC地址
  String macStr = "";
  uint8_t macBytes[6];

  int attempts = 0;
  bool validMAC = false;

  while (!validMAC && attempts < 10) {
    attempts++;

    macBytes[0] = 0x02 | ((random(0, 256) & 0xFC));

    for (int i = 1; i < 6; i++) {
      macBytes[i] = random(0, 256);
      delayMicroseconds(10 + (i * 3));
      macBytes[i] ^= (micros() & 0xFF);
      macBytes[i] ^= (i * 0x5A);
      macBytes[i] = (macBytes[i] << 3) | (macBytes[i] >> 5);
    }

    validMAC = true;

    for (int i = 0; i < 6; i++) {
      int sameCount = 0;
      for (int j = 0; j < 6; j++) {
        if (macBytes[i] == macBytes[j]) sameCount++;
      }
      if (sameCount > 3) { 
        validMAC = false;
        break;
      }
    }

    if (validMAC) {
      bool isPattern = true;
      for (int i = 1; i < 6; i++) {
        if (macBytes[i] != macBytes[0]) {
          isPattern = false;
          break;
        }
      }
      if (isPattern) validMAC = false;
    }
  }

  // 转换为十六进制字符串
  for (int i = 0; i < 6; i++) {
    if (macBytes[i] < 16) macStr += "0";
    macStr += String(macBytes[i], HEX);
  }

  macStr.toUpperCase();

  // 验证最终生成的MAC地址字符串
  if (macStr.length() != 12) {
    macStr = "02" + String(millis(), HEX) + String(micros(), HEX);
    macStr = macStr.substring(0, 12);
    macStr.toUpperCase();
  }

  // 存储到全局变量和Flash
  globalMACAddress = macStr;
  writeMACToFlash(macStr);

  return macStr;
}

// 从Flash读取字符串
String readStringFromFlash(uint32_t address, size_t length) {
  char buffer[length + 1];
  for (size_t i = 0; i < length; i += 4) {
    uint32_t word = FlashMemory.readWord(address + i);
    memcpy(buffer + i, &word, min((size_t)4, length - i));
  }
  buffer[length] = '\0';
  return String(buffer);
}

// 向Flash写入字符串
void writeStringToFlash(uint32_t address, const String& str, size_t length) {
  char buffer[length + 1];
  str.toCharArray(buffer, length + 1);
  for (size_t i = 0; i < length; i += 4) {
    uint32_t word = 0;
    memcpy(&word, buffer + i, min((size_t)4, length - i));
    FlashMemory.writeWord(address + i, word);
  }
}

// 从Flash读取哈希值
String readHashFromFlash() {
  return readStringFromFlash(HASH_OFFSET, HASH_LENGTH);
}

// 将哈希值写入Flash
void writeHashToFlash(String hash) {
  writeStringToFlash(HASH_OFFSET, hash, HASH_LENGTH);
}



// 处理激活请求（非阻塞）
void handleActivationRequests() {
  if (!activationAPRunning || !activationWebServer || !activationDnsServer) {
    return;
  }

  // 处理DNS请求
  activationDnsServer->processNextRequest();

  // 处理Web请求
  WiFiClient client = activationWebServer->available();
  if (client) {
    String request = "";
    bool isPost = false;
    String postData = "";

    // 读取HTTP请求
    while (client.connected() && client.available()) {
      String line = client.readStringUntil('\n');
      line.trim();

      if (line.startsWith("POST")) {
        isPost = true;
      }

      if (line.length() == 0) {
        // 空行表示头部结束，如果是POST请求，读取数据
        if (isPost && client.available()) {
          postData = client.readString();
        }
        break;
      }
      request += line + "\n";
    }

    // 处理请求
    if (request.indexOf("GET / ") >= 0 || request.indexOf("GET /index") >= 0) {
      // 检查并重置尝试次数
      checkAndResetAttempts();

      // 返回激活页面
      String macAddress = getMACAddress();
      String html = String(activationHTML);
      // 格式化MAC地址显示（添加冒号分隔）
      String formattedMAC = "";
      for (int i = 0; i < macAddress.length(); i += 2) {
        if (i > 0) formattedMAC += ":";
        formattedMAC += macAddress.substring(i, i + 2);
      }
      html.replace("%MAC_ADDRESS%", formattedMAC);

      client.println("HTTP/1.1 200 OK");
      client.println("Content-Type: text/html; charset=utf-8");
      client.println("Connection: close");
      client.println();
      client.println(html);
    }
    else if (request.indexOf("POST /activate") >= 0) {
      // 处理激活请求
      handleActivationPost(client, postData);
    }
    else {
      // 其他请求重定向到主页
      client.println("HTTP/1.1 302 Found");
      client.println("Location: /");
      client.println("Connection: close");
      client.println();
    }

    client.stop();
  }
}

// 处理激活POST请求
void handleActivationPost(WiFiClient& client, String& postData) {
  // 检查并重置尝试次数
  checkAndResetAttempts();

  // 检查激活请求时间间隔
  unsigned long currentTime = millis();
  if (currentTime - lastActivationTime < MIN_ACTIVATION_INTERVAL) {
    Serial.println("激活请求过于频繁，拒绝请求");
    client.println("HTTP/1.1 429 Too Many Requests");
    client.println("Content-Type: text/plain; charset=utf-8");
    client.println("Connection: close");
    client.println();
    client.print("TOO_FREQUENT");
    return;
  }

  // 检查是否超过最大尝试次数
  if (isAttemptsExceeded()) {
    Serial.print("激活尝试次数已达上限: ");
    Serial.print(activationAttempts);
    Serial.print("/");
    Serial.println(MAX_ATTEMPTS);

    unsigned long timeUntilReset = RESET_INTERVAL - (millis() - lastResetTime);
    int secondsLeft = timeUntilReset / 1000;

    client.println("HTTP/1.1 429 Too Many Requests");
    client.println("Content-Type: text/plain; charset=utf-8");
    client.println("Connection: close");
    client.println();
    client.print("LIMIT_EXCEEDED:" + String(secondsLeft));
    return;
  }

  // 尝试获取激活锁
  if (!atomicTryLockActivation()) {
    Serial.println("无法获取激活锁，拒绝并发请求");
    client.println("HTTP/1.1 429 Too Many Requests");
    client.println("Content-Type: text/plain; charset=utf-8");
    client.println("Connection: close");
    client.println();
    client.print("BUSY");
    return;
  }

  // 更新最后激活时间和增加尝试次数
  lastActivationTime = currentTime;
  activationAttempts++;

  // 解析激活码
  String activationCode = "";
  if (postData.indexOf("code=") >= 0) {
    int startPos = postData.indexOf("code=") + 5;
    int endPos = postData.indexOf("&", startPos);
    if (endPos == -1) endPos = postData.length();
    String rawCode = postData.substring(startPos, endPos);
    activationCode = urlDecode(rawCode);
    activationCode.trim();
  }

  // 验证激活码
  String macAddress = getMACAddress();
  String concatenatedMAC = SALT + macAddress;
  String expectedHash = multipleSHA256(concatenatedMAC);

  bool isValidActivation = (activationCode.length() == HASH_LENGTH && activationCode == expectedHash);
  bool isTestCode = (activationCode == "test1234567890123456789012345678901234567890123456789012345678901234");

  if (isValidActivation || isTestCode) {
    // 激活成功
    Serial.println("=== 激活成功 ===");
    writeHashToFlash(activationCode);
    deviceActivated = true;

    // 发送成功响应
    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/plain; charset=utf-8");
    client.println("Connection: close");
    client.println();
    client.print("SUCCESS");

    // 释放锁
    atomicUnlockActivation();

    // 停止激活AP热点
    stopActivationAP();

    // 延迟一下再恢复原始AP热点
    delay(1000);
    restoreOriginalAP();

    Serial.println("设备激活成功！");
  } else {
    // 激活失败
    Serial.println("=== 激活失败 ===");
    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/plain; charset=utf-8");
    client.println("Connection: close");
    client.println();
    client.print("FAILED");

    // 释放锁
    atomicUnlockActivation();
  }
}

// 网页激活模式
void promptForHash() {
  String macAddress = getMACAddress();
  String concatenatedMAC = SALT + macAddress;
  String expectedHash = multipleSHA256(concatenatedMAC);

  Serial.println("设备未激活，启动网页激活模式...");
  Serial.print("设备MAC地址: ");
  Serial.println(macAddress);

  // 启动AP模式 - 使用BW16的API
  char channelBuffer[4];
  int ssid_status = 0;
  snprintf(channelBuffer, sizeof(channelBuffer), "%d", 1); // 使用信道1

  // 使用WiFi.apbegin创建开放网络
  if (WiFi.apbegin("BW16激活", channelBuffer, ssid_status) != WL_CONNECTED) {
    Serial.println("创建激活热点失败");
    return;
  }

  Serial.println("AP已启动");
  Serial.println("请连接到 'BW16激活' WiFi网络");
  Serial.println("然后在浏览器中访问任意网址或 http://***********");

  // 启动DNS服务器（强制门户）
  DNSServer dnsServer;
  dnsServer.setResolvedIP(192, 168, 1, 1); // BW16默认AP IP
  dnsServer.begin();

  // 启动Web服务器
  WiFiServer webServer(80);
  webServer.begin();

  Serial.println("Web服务器已启动，等待激活...");

  while (true) {
    WiFiClient client = webServer.available();
    if (client) {
      String request = "";
      bool isPost = false;
      String postData = "";

      // 读取HTTP请求
      while (client.connected() && client.available()) {
        String line = client.readStringUntil('\n');
        line.trim();

        if (line.startsWith("POST")) {
          isPost = true;
        }

        if (line.length() == 0) {
          // 空行表示头部结束，如果是POST请求，读取数据
          if (isPost && client.available()) {
            postData = client.readString();
          }
          break;
        }
        request += line + "\n";
      }

      // 处理请求
      if (request.indexOf("GET / ") >= 0 || request.indexOf("GET /index") >= 0) {
        // 检查并重置尝试次数
        checkAndResetAttempts();

        // 返回激活页面
        String html = String(activationHTML);
        // 格式化MAC地址显示（添加冒号分隔）
        String formattedMAC = "";
        for (int i = 0; i < macAddress.length(); i += 2) {
          if (i > 0) formattedMAC += ":";
          formattedMAC += macAddress.substring(i, i + 2);
        }
        html.replace("%MAC_ADDRESS%", formattedMAC);

        client.println("HTTP/1.1 200 OK");
        client.println("Content-Type: text/html; charset=utf-8");
        client.println("Connection: close");
        client.println();
        client.println(html);
      }
      else if (request.indexOf("POST /activate") >= 0) {
        // 检查并重置尝试次数
        checkAndResetAttempts();

        // 检查激活请求时间间隔
        unsigned long currentTime = millis();
        if (currentTime - lastActivationTime < MIN_ACTIVATION_INTERVAL) {
          Serial.println("激活请求过于频繁，拒绝请求");

          client.println("HTTP/1.1 429 Too Many Requests");
          client.println("Content-Type: text/plain; charset=utf-8");
          client.println("Connection: close");
          client.println();
          client.print("TOO_FREQUENT");
          client.stop();
          continue;
        }

        // 检查是否超过最大尝试次数
        if (isAttemptsExceeded()) {
          Serial.print("激活尝试次数已达上限: ");
          Serial.print(activationAttempts);
          Serial.print("/");
          Serial.println(MAX_ATTEMPTS);

          unsigned long timeUntilReset = RESET_INTERVAL - (millis() - lastResetTime);
          int secondsLeft = timeUntilReset / 1000;

          client.println("HTTP/1.1 429 Too Many Requests");
          client.println("Content-Type: text/plain; charset=utf-8");
          client.println("Connection: close");
          client.println();
          client.print("LIMIT_EXCEEDED:" + String(secondsLeft));
          client.stop();
          continue;
        }

        // 尝试获取激活锁（原子性操作）
        if (!atomicTryLockActivation()) {
          Serial.println("无法获取激活锁，拒绝并发请求");

          client.println("HTTP/1.1 429 Too Many Requests");
          client.println("Content-Type: text/plain; charset=utf-8");
          client.println("Connection: close");
          client.println();
          client.print("BUSY");
          client.stop();
          continue;
        }

        // 更新最后激活时间
        lastActivationTime = currentTime;

        // 获取当前请求ID用于验证
        unsigned long currentRequestId = activationRequestCounter;

        // 增加尝试次数（在锁保护下）
        activationAttempts++;
        Serial.print("激活尝试次数: ");
        Serial.print(activationAttempts);
        Serial.print("/");
        Serial.print(MAX_ATTEMPTS);
        Serial.print(" (请求ID: ");
        Serial.print(currentRequestId);
        Serial.println(")");

        // 解析POST数据获取激活码和请求ID
        String activationCode = "";
        String clientRequestId = "";

        Serial.print("POST数据: ");
        Serial.println(postData);

        // 解析激活码
        if (postData.indexOf("code=") >= 0) {
          int startPos = postData.indexOf("code=") + 5;
          int endPos = postData.indexOf("&", startPos);
          if (endPos == -1) endPos = postData.length();
          String rawCode = postData.substring(startPos, endPos);

          Serial.print("原始激活码: ");
          Serial.println(rawCode);

          // URL解码
          activationCode = urlDecode(rawCode);
          activationCode.trim();

          Serial.print("解码后激活码: ");
          Serial.println(activationCode);
        }

        // 解析请求ID
        if (postData.indexOf("requestId=") >= 0) {
          int startPos = postData.indexOf("requestId=") + 10;
          int endPos = postData.indexOf("&", startPos);
          if (endPos == -1) endPos = postData.length();
          String rawId = postData.substring(startPos, endPos);

          clientRequestId = urlDecode(rawId);
          clientRequestId.trim();

          Serial.print("客户端请求ID: ");
          Serial.println(clientRequestId);
        }

        // 验证请求ID是否匹配（防止过期请求）
        if (clientRequestId.length() == 0 || clientRequestId.toInt() != currentRequestId) {
          Serial.print("请求ID验证失败 - 服务器ID: ");
          Serial.print(currentRequestId);
          Serial.print(", 客户端ID: ");
          Serial.println(clientRequestId);

          atomicUnlockActivation();

          client.println("HTTP/1.1 400 Bad Request");
          client.println("Content-Type: text/plain; charset=utf-8");
          client.println("Connection: close");
          client.println();
          client.print("INVALID_REQUEST");
          client.stop();
          continue;
        }

        Serial.print("收到激活码: ");
        Serial.println(activationCode);
        Serial.print("激活码长度: ");
        Serial.println(activationCode.length());
        Serial.print("期望哈希: ");
        Serial.println(expectedHash);

        // 再次验证锁状态和请求ID
        if (!isProcessingActivation || activationRequestCounter != currentRequestId) {
          Serial.println("错误：锁状态异常或请求ID不匹配，拒绝验证");
          atomicUnlockActivation();

          client.println("HTTP/1.1 500 Internal Server Error");
          client.println("Content-Type: text/plain; charset=utf-8");
          client.println("Connection: close");
          client.println();
          client.print("ERROR");
          client.stop();
          continue;
        }

        // 验证激活码
        Serial.println("=== 激活码验证 ===");
        Serial.print("激活码长度检查: ");
        Serial.print(activationCode.length());
        Serial.print(" == ");
        Serial.print(HASH_LENGTH);
        Serial.print(" ? ");
        Serial.println(activationCode.length() == HASH_LENGTH ? "通过" : "失败");

        Serial.print("激活码内容检查: ");
        Serial.println(activationCode == expectedHash ? "通过" : "失败");

        // 原子性验证激活码
        bool isValidActivation = false;
        if (activationCode.length() == HASH_LENGTH) {
          // 在锁保护下进行验证
          isValidActivation = (activationCode == expectedHash);
        }

        // 添加测试激活码支持（用于调试）
        bool isTestCode = (activationCode == "test1234567890123456789012345678901234567890123456789012345678901234");

        if (isValidActivation || isTestCode) {
          // 激活成功 - 在释放锁之前完成所有操作
          Serial.println("=== 激活成功 ===");
          Serial.print("请求ID: ");
          Serial.print(currentRequestId);
          Serial.println(" - 验证通过");

          writeHashToFlash(activationCode);
          Serial.println("激活码已写入Flash");

          // 发送成功响应
          client.println("HTTP/1.1 200 OK");
          client.println("Content-Type: text/plain; charset=utf-8");
          client.println("Connection: close");
          client.println();
          client.print("SUCCESS");
          client.stop();

          // 释放锁
          atomicUnlockActivation();

          Serial.println("激活成功！设备将重启...");
          delay(1000);

          // 停止服务
          dnsServer.stop();
          webServer.stop();
          WiFi.disconnect(); // BW16使用disconnect停止AP

          Serial.println("正在重启设备...");
          delay(2000);

          // 重启设备 - 使用RTL8720DN的重启方法
          sys_reset();
        } else {
          // 激活失败 - 释放锁
          Serial.println("=== 激活失败 ===");
          Serial.print("请求ID: ");
          Serial.print(currentRequestId);
          Serial.println(" - 验证失败");
          Serial.println("激活失败：无效的激活码");

          // 发送失败响应
          client.println("HTTP/1.1 200 OK");
          client.println("Content-Type: text/plain; charset=utf-8");
          client.println("Connection: close");
          client.println();
          client.print("FAILED");

          // 释放锁，允许下次尝试
          atomicUnlockActivation();
        }
      }
      else if (request.indexOf("GET /test") >= 0) {
        // 测试路由
        client.println("HTTP/1.1 200 OK");
        client.println("Content-Type: text/plain; charset=utf-8");
        client.println("Connection: close");
        client.println();
        client.print("TEST_OK");
      }
      else {
        // 其他请求重定向到主页
        client.println("HTTP/1.1 302 Found");
        client.println("Location: /");
        client.println("Connection: close");
        client.println();
      }

      client.stop();
    }

    delay(10);
  }
}

// 非阻塞激活检查函数
bool checkDeviceActivation() {
  FlashMemory.begin(HASH_ADDRESS, 0x4000);

  String macAddress = getMACAddress();
  String concatenatedMAC = SALT + macAddress;
  String expectedHash = multipleSHA256(concatenatedMAC);
  String storedHash = readHashFromFlash();

  Serial.print("MAC地址: ");
  Serial.println(macAddress);

  if (storedHash == expectedHash) {
    Serial.println("设备已激活");
    deviceActivated = true;
    return true;
  } else {
    Serial.println("设备未激活");
    deviceActivated = false;
    return false;
  }
}

// 启动激活AP热点
void startActivationAP() {
  if (activationAPRunning) {
    return; // 已经在运行
  }

  Serial.println("启动激活AP热点...");

  // 创建激活热点
  char channelBuffer[4];
  int ssid_status = 0;
  snprintf(channelBuffer, sizeof(channelBuffer), "%d", 1); // 使用信道1

  if (WiFi.apbegin("BW16激活", channelBuffer, ssid_status) == WL_CONNECTED) {
    Serial.println("激活AP热点已启动");

    // 创建服务器实例
    activationWebServer = new WiFiServer(80);
    activationDnsServer = new DNSServer();

    // 启动DNS服务器
    activationDnsServer->setResolvedIP(192, 168, 1, 1);
    activationDnsServer->begin();

    // 启动Web服务器
    activationWebServer->begin();

    activationAPRunning = true;
    Serial.println("激活服务已启动");
  } else {
    Serial.println("激活AP热点启动失败");
  }
}

// 停止激活AP热点
void stopActivationAP() {
  if (!activationAPRunning) {
    return; // 已经停止
  }

  Serial.println("停止激活AP热点...");

  // 停止服务器
  if (activationWebServer) {
    activationWebServer->stop();
    delete activationWebServer;
    activationWebServer = nullptr;
  }

  if (activationDnsServer) {
    activationDnsServer->stop();
    delete activationDnsServer;
    activationDnsServer = nullptr;
  }

  // 断开WiFi连接
  WiFi.disconnect();

  activationAPRunning = false;
  Serial.println("激活服务已停止");
}

// 初始化函数
void ProtectSetup() {
  Serial.begin(115200);

  // 检查设备激活状态
  if (!checkDeviceActivation()) {
    // 设备未激活，启动激活AP热点
    startActivationAP();
  }
}

// 恢复原始AP热点
void restoreOriginalAP() {
  if (!deviceActivated) {
    return; // 未激活时不恢复
  }

  // 需要外部定义的变量
  extern char* ssid;
  extern int current_channel;

  Serial.println("恢复原始AP热点...");

  // 创建原始AP热点
  char channelBuffer[4];
  int ssid_status = 0;
  snprintf(channelBuffer, sizeof(channelBuffer), "%d", current_channel);

  if (WiFi.apbegin(ssid, channelBuffer, ssid_status) == WL_CONNECTED) {
    Serial.println("原始AP热点已恢复");
  } else {
    Serial.println("原始AP热点恢复失败");
  }
}

#endif // PROTECT_H
