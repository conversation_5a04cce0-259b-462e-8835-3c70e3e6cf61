<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BW16设备激活码生成器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .input-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
            font-family: monospace;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
            margin-top: 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            position: relative;
        }
        .copy-btn {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 10px;
            display: inline-block;
        }
        .copy-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        .copy-btn:active {
            transform: translateY(0);
        }
        .copy-success {
            background: #28a745 !important;
        }
        .activation-code {
            word-break: break-all;
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            color: #856404;
        }
        .example {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 BW16设备激活码生成器</h1>

        <div class="input-group">
            <label for="macInput">请输入设备MAC地址：</label>
            <input type="text" id="macInput" placeholder="支持任意格式，如：A1:B2:C3:D4:E5:F6" maxlength="17">
            <div class="example">支持格式：A1B2C3D4E5F6 或 A1:B2:C3:D4:E5:F6 或 A1-B2-C3-D4-E5-F6</div>
            <div id="macPreview" style="margin-top: 8px; font-size: 14px; font-weight: bold;"></div>
        </div>

        <button class="btn" onclick="generateActivationCode()">生成激活码</button>

        <div id="result"></div>

        <div class="note">
            <strong>使用说明：</strong><br>
            1. 输入设备的MAC地址（支持任意格式，会自动过滤）<br>
            2. 支持格式：A1B2C3D4E5F6 或 A1:B2:C3:D4:E5:F6 或 A1-B2-C3-D4-E5-F6<br>
            3. 点击生成激活码按钮<br>
            4. 复制生成的64位激活码<br>
            5. 在设备激活页面中输入此激活码
        </div>
    </div>

    <script>
        // MAC地址过滤函数
        function filterMacAddress(input) {
            // 移除所有非十六进制字符（保留0-9, A-F, a-f）
            var filtered = input.replace(/[^0-9A-Fa-f]/g, '');
            // 转换为大写
            return filtered.toUpperCase();
        }

        // 格式化MAC地址显示（添加冒号）
        function formatMacAddress(mac) {
            if (mac.length !== 12) return mac;
            return mac.replace(/(.{2})/g, '$1:').slice(0, -1);
        }

        // 实时过滤输入
        function filterInput() {
            var input = document.getElementById('macInput');
            var rawValue = input.value;
            var filteredValue = filterMacAddress(rawValue);

            // 限制最大长度为12位
            if (filteredValue.length > 12) {
                filteredValue = filteredValue.substring(0, 12);
            }

            // 更新输入框值
            input.value = filteredValue;

            // 实时显示格式化的MAC地址
            updateMacPreview(filteredValue);
        }

        // 更新MAC地址预览
        function updateMacPreview(mac) {
            var previewDiv = document.getElementById('macPreview');
            if (mac.length === 12) {
                var formatted = formatMacAddress(mac);
                previewDiv.innerHTML = '<strong>过滤后：</strong> ' + formatted;
                previewDiv.style.color = '#28a745';
            } else if (mac.length > 0) {
                previewDiv.innerHTML = '<strong>当前长度：</strong> ' + mac.length + '/12';
                previewDiv.style.color = '#ffc107';
            } else {
                previewDiv.innerHTML = '';
            }
        }

        function generateActivationCode() {
            // 获取输入值并自动过滤
            var rawInput = document.getElementById('macInput').value.trim();
            var macInput = filterMacAddress(rawInput);
            var resultDiv = document.getElementById('result');

            // 验证过滤后的MAC地址格式
            if (macInput.length !== 12) {
                resultDiv.innerHTML = '<div class="result" style="background: #f8d7da; border-color: #f5c6cb; color: #721c24;">错误：MAC地址必须是12位十六进制字符<br>当前长度：' + macInput.length + '</div>';
                return;
            }

            // 验证是否为有效的十六进制
            if (!/^[0-9A-F]{12}$/.test(macInput)) {
                resultDiv.innerHTML = '<div class="result" style="background: #f8d7da; border-color: #f5c6cb; color: #721c24;">错误：MAC地址只能包含0-9和A-F字符<br>过滤后：' + macInput + '</div>';
                return;
            }

            // 与盐值连接
            var concatenatedInput = "SALTYFISH" + macInput;

            // 执行2次SHA256哈希
            var currentHash = concatenatedInput;
            for (var i = 0; i < 2; i++) {
                currentHash = CryptoJS.SHA256(currentHash).toString();
            }

            // 格式化MAC地址显示
            var formattedMAC = macInput.replace(/(.{2})/g, '$1:').slice(0, -1);

            // 显示结果
            resultDiv.innerHTML =
                '<div class="result success">' +
                '<strong>设备MAC地址：</strong> ' + formattedMAC + '<br><br>' +
                '<strong>激活码：</strong><br>' +
                '<div class="activation-code" id="activationCodeText">' + currentHash + '</div>' +
                '<button class="copy-btn" onclick="copyActivationCode()" id="copyBtn">复制激活码</button>' +
                '</div>';
        }

        // 复制激活码
        function copyActivationCode() {
            const codeElement = document.getElementById('activationCodeText');
            const codeText = codeElement.textContent;
            const copyBtn = document.getElementById('copyBtn');

            if (navigator.clipboard && window.isSecureContext) {
                // 现代浏览器的异步API
                navigator.clipboard.writeText(codeText).then(function() {
                    showCopySuccess(copyBtn, '已复制');
                }).catch(function() {
                    fallbackCopyTextToClipboard(codeText, copyBtn);
                });
            } else {
                // 兼容旧浏览器
                fallbackCopyTextToClipboard(codeText, copyBtn);
            }
        }

        // 兼容性复制函数
        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess(button, '已复制');
            } catch (err) {
                showCopySuccess(button, '复制失败');
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功状态
        function showCopySuccess(button, message) {
            const originalText = button.textContent;
            button.textContent = message;
            button.classList.add('copy-success');

            setTimeout(function() {
                button.textContent = originalText;
                button.classList.remove('copy-success');
            }, 2000);
        }

        // 回车键生成
        document.getElementById('macInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                generateActivationCode();
            }
        });

        // 实时过滤输入
        document.getElementById('macInput').addEventListener('input', function(e) {
            filterInput();
        });

        // 粘贴事件处理
        document.getElementById('macInput').addEventListener('paste', function(e) {
            // 延迟处理，确保粘贴内容已经插入
            setTimeout(function() {
                filterInput();
            }, 10);
        });
    </script>
</body>
</html>


